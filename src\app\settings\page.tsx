'use client';

import React, { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { User } from '@/types';
import { validateEmail } from '@/lib/utils';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/Card';
import { authApi } from '@/lib/api/auth';
import toast from 'react-hot-toast';
import { User as UserIcon, Lock, Save } from 'lucide-react';

export default function SettingsPage() {
  const { user, updateProfile } = useAuth();
  
  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    email: user?.email || '',
  });
  
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  
  const [profileLoading, setProfileLoading] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [profileErrors, setProfileErrors] = useState<Partial<typeof profileData>>({});
  const [passwordErrors, setPasswordErrors] = useState<Partial<typeof passwordData>>({});

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (profileErrors[name as keyof typeof profileData]) {
      setProfileErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (passwordErrors[name as keyof typeof passwordData]) {
      setPasswordErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateProfileForm = (): boolean => {
    const newErrors: Partial<typeof profileData> = {};

    if (!profileData.name.trim()) {
      newErrors.name = 'Họ tên là bắt buộc';
    } else if (profileData.name.trim().length < 2) {
      newErrors.name = 'Họ tên phải có ít nhất 2 ký tự';
    }

    if (!profileData.email) {
      newErrors.email = 'Email là bắt buộc';
    } else if (!validateEmail(profileData.email)) {
      newErrors.email = 'Email không hợp lệ';
    }

    setProfileErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validatePasswordForm = (): boolean => {
    const newErrors: Partial<typeof passwordData> = {};

    if (!passwordData.currentPassword) {
      newErrors.currentPassword = 'Mật khẩu hiện tại là bắt buộc';
    }

    if (!passwordData.newPassword) {
      newErrors.newPassword = 'Mật khẩu mới là bắt buộc';
    } else if (passwordData.newPassword.length < 6) {
      newErrors.newPassword = 'Mật khẩu mới phải có ít nhất 6 ký tự';
    }

    if (!passwordData.confirmPassword) {
      newErrors.confirmPassword = 'Xác nhận mật khẩu là bắt buộc';
    } else if (passwordData.confirmPassword !== passwordData.newPassword) {
      newErrors.confirmPassword = 'Mật khẩu xác nhận không khớp';
    }

    setPasswordErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateProfileForm()) return;

    try {
      setProfileLoading(true);
      await updateProfile(profileData);
    } catch (error) {
      // Error is handled by AuthContext
      console.error('Profile update error:', error);
    } finally {
      setProfileLoading(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validatePasswordForm()) return;

    try {
      setPasswordLoading(true);
      await authApi.changePassword({
        currentPassword: passwordData.currentPassword,
        newPassword: passwordData.newPassword,
      });
      
      // Clear form on success
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
      
      toast.success('Đổi mật khẩu thành công!');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Đổi mật khẩu thất bại';
      toast.error(message);
    } finally {
      setPasswordLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Cài đặt tài khoản</h1>
          <p className="text-gray-600">Quản lý thông tin cá nhân và bảo mật</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Profile Settings */}
          <Card>
            <form onSubmit={handleProfileSubmit}>
              <CardHeader>
                <div className="flex items-center">
                  <UserIcon className="mr-2 h-5 w-5" />
                  <CardTitle>Thông tin cá nhân</CardTitle>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <Input
                  label="Họ và tên"
                  type="text"
                  name="name"
                  value={profileData.name}
                  onChange={handleProfileChange}
                  error={profileErrors.name}
                  placeholder="Nguyễn Văn A"
                  required
                />

                <Input
                  label="Email"
                  type="email"
                  name="email"
                  value={profileData.email}
                  onChange={handleProfileChange}
                  error={profileErrors.email}
                  placeholder="<EMAIL>"
                  required
                />

                <div className="bg-gray-50 p-3 rounded-md">
                  <p className="text-sm text-gray-600">
                    <strong>Vai trò:</strong> {user?.role === 'admin' ? 'Quản trị viên' : user?.role === 'teacher' ? 'Giáo viên' : 'Học sinh'}
                  </p>
                  <p className="text-sm text-gray-600">
                    <strong>Tham gia:</strong> {user?.created_at ? new Date(user.created_at).toLocaleDateString('vi-VN') : 'N/A'}
                  </p>
                </div>
              </CardContent>

              <CardFooter>
                <Button
                  type="submit"
                  loading={profileLoading}
                  disabled={profileLoading}
                  className="w-full"
                >
                  <Save className="mr-2 h-4 w-4" />
                  {profileLoading ? 'Đang cập nhật...' : 'Cập nhật thông tin'}
                </Button>
              </CardFooter>
            </form>
          </Card>

          {/* Password Settings */}
          <Card>
            <form onSubmit={handlePasswordSubmit}>
              <CardHeader>
                <div className="flex items-center">
                  <Lock className="mr-2 h-5 w-5" />
                  <CardTitle>Đổi mật khẩu</CardTitle>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <Input
                  label="Mật khẩu hiện tại"
                  type="password"
                  name="currentPassword"
                  value={passwordData.currentPassword}
                  onChange={handlePasswordChange}
                  error={passwordErrors.currentPassword}
                  placeholder="••••••••"
                  required
                />

                <Input
                  label="Mật khẩu mới"
                  type="password"
                  name="newPassword"
                  value={passwordData.newPassword}
                  onChange={handlePasswordChange}
                  error={passwordErrors.newPassword}
                  placeholder="••••••••"
                  helperText="Mật khẩu phải có ít nhất 6 ký tự"
                  required
                />

                <Input
                  label="Xác nhận mật khẩu mới"
                  type="password"
                  name="confirmPassword"
                  value={passwordData.confirmPassword}
                  onChange={handlePasswordChange}
                  error={passwordErrors.confirmPassword}
                  placeholder="••••••••"
                  required
                />
              </CardContent>

              <CardFooter>
                <Button
                  type="submit"
                  loading={passwordLoading}
                  disabled={passwordLoading}
                  className="w-full"
                >
                  <Lock className="mr-2 h-4 w-4" />
                  {passwordLoading ? 'Đang đổi...' : 'Đổi mật khẩu'}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </div>

        {/* Security Info */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Bảo mật tài khoản</CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-gray-600 space-y-2">
            <p>• Sử dụng mật khẩu mạnh với ít nhất 6 ký tự</p>
            <p>• Không chia sẻ thông tin đăng nhập với người khác</p>
            <p>• Đăng xuất sau khi sử dụng trên máy tính chung</p>
            <p>• Liên hệ quản trị viên nếu phát hiện hoạt động bất thường</p>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
