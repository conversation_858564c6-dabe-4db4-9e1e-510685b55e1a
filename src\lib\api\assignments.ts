import { apiClient } from './client';

export interface Assignment {
  id: number;
  title: string;
  description?: string;
  type: 'homework' | 'exam' | 'quiz';
  deadline: string;
  classroom_id: number;
  teacher_id: number;
  question_files: FileInfo[];
  solution_files?: FileInfo[];
  max_score: number;
  is_active: boolean;
  instructions?: string;
  created_at: string;
  updated_at: string;

  // Additional fields from API
  teacher_name?: string;
  classroom_name?: string;
  submission_count?: number;
  user_submitted?: number;
  total_students?: number;
  is_expired?: boolean;
  can_submit?: boolean;
  can_view_solution?: boolean;
  user_has_submitted?: boolean;
  user_submission?: AssignmentSubmission;
}

export interface AssignmentSubmission {
  id: number;
  assignment_id: number;
  student_id: number;
  submission_files: FileInfo[];
  submitted_at: string;
  score?: number;
  feedback?: string;
  status: 'submitted' | 'graded' | 'returned';
  is_late: boolean;
  created_at: string;
  updated_at: string;
  
  // Additional fields from API
  student_name?: string;
  student_email?: string;
  student_grade?: number;
  assignment_title?: string;
  deadline?: string;
  max_score?: number;
}

export interface FileInfo {
  filename: string;
  originalname: string;
  originalName?: string; // For backward compatibility
  mimetype: string;
  size: number;
  path: string;
  url: string;
  publicId?: string; // Cloudinary public ID
  uploadedAt?: string; // For upload timestamp
}

export interface CreateAssignmentData {
  title: string;
  description?: string;
  type?: 'homework' | 'exam' | 'quiz';
  deadline: string;
  classroom_id: number;
  max_score?: number;
  instructions?: string;
}

export interface UpdateAssignmentData {
  title?: string;
  description?: string;
  type?: 'homework' | 'exam' | 'quiz';
  deadline?: string;
  max_score?: number;
  instructions?: string;
  is_active?: boolean;
}

export interface GradeSubmissionData {
  score?: number;
  feedback?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export const assignmentsApi = {
  // Get assignments for a classroom
  getClassroomAssignments: async (
    classroomId: number,
    page = 0,
    limit = 10
  ): Promise<PaginatedResponse<Assignment>> => {
    const response = await apiClient.get(`/assignments/classroom/${classroomId}?page=${page}&limit=${limit}`);
    return {
      data: response.data.data.assignments,
      pagination: response.data.data.pagination
    };
  },

  // Get assignment details
  getAssignment: async (id: number): Promise<Assignment> => {
    const response = await apiClient.get(`/assignments/${id}`);
    return response.data.data;
  },

  // Get assignments by classroom ID
  getAssignmentsByClassroom: async (classroomId: number): Promise<Assignment[]> => {
    const response = await apiClient.get(`/classrooms/${classroomId}/assignments`);
    return response.data.data;
  },

  // Create new assignment (Teacher only)
  createAssignment: async (data: CreateAssignmentData): Promise<Assignment> => {
    const response = await apiClient.post('/assignments', data);
    return response.data.data;
  },

  // Update assignment (Teacher only)
  updateAssignment: async (id: number, data: UpdateAssignmentData): Promise<Assignment> => {
    const response = await apiClient.put(`/assignments/${id}`, data);
    return response.data.data;
  },

  // Delete assignment (Teacher only)
  deleteAssignment: async (id: number): Promise<void> => {
    await apiClient.delete(`/assignments/${id}`);
  },

  // Get assignment submissions (Teacher only)
  getAssignmentSubmissions: async (
    assignmentId: number, 
    page = 0, 
    limit = 10, 
    status?: string
  ): Promise<PaginatedResponse<AssignmentSubmission>> => {
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    if (status) {
      params.append('status', status);
    }
  
    const response = await apiClient.get(`/assignments/${assignmentId}/submissions?${params.toString()}`);
    return {
      data: response.data.data.submissions,
      pagination: response.data.data.pagination
    };
  },

  // Submit assignment (Student only)
  submitAssignment: async (assignmentId: number, files: File[]): Promise<AssignmentSubmission> => {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    const response = await apiClient.post(`/cloudinary-upload/submission/${assignmentId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.data.submission;
  },

  // Get student's own submission
  getMySubmission: async (assignmentId: number): Promise<AssignmentSubmission> => {
    const response = await apiClient.get(`/submissions/my/${assignmentId}`);
    return response.data.data;
  },

  // Grade submission (Teacher only)
  gradeSubmission: async (submissionId: number, data: GradeSubmissionData): Promise<AssignmentSubmission> => {
    const response = await apiClient.put(`/submissions/${submissionId}/grade`, data);
    return response.data.data;
  },

  // Get students who haven't submitted assignment (Teacher only)
  getMissingSubmissions: async (assignmentId: number): Promise<{
    assignment: { id: number; title: string; deadline: string };
    missing_students: Array<{
      id: number;
      name: string;
      email: string;
      grade: number;
      enrolled_at: string;
    }>;
    total_missing: number;
  }> => {
    const response = await apiClient.get(`/assignments/${assignmentId}/missing-submissions`);
    return response.data.data;
  },

  // Upload question files (Teacher only)
  uploadQuestionFiles: async (assignmentId: number, files: File[]): Promise<FileInfo[]> => {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    const response = await apiClient.post(`/cloudinary-upload/question/${assignmentId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.data.files;
  },

  // Upload solution files (Teacher only)
  uploadSolutionFiles: async (assignmentId: number, files: File[]): Promise<FileInfo[]> => {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    const response = await apiClient.post(`/cloudinary-upload/solution/${assignmentId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.data.files;
  },

  // Delete solution file (Teacher only)
  deleteSolutionFile: async (assignmentId: number, fileIndex: number): Promise<void> => {
    await apiClient.delete(`/cloudinary-upload/solution/${assignmentId}/${fileIndex}`);
  },

  // Delete question file (Teacher only)
  deleteQuestionFile: async (assignmentId: number, fileIndex: number): Promise<void> => {
    await apiClient.delete(`/cloudinary-upload/question/${assignmentId}/${fileIndex}`);
  },

  // Helper function to check if deadline has passed
  isExpired: (deadline: string): boolean => {
    return new Date() > new Date(deadline);
  },

  // Helper function to get time remaining
  getTimeRemaining: (deadline: string) => {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diff = deadlineDate.getTime() - now.getTime();
    
    if (diff <= 0) return null;
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return { days, hours, minutes, total: diff };
  },

  // Helper function to format file size
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // Helper function to get file type icon
  getFileTypeIcon: (mimetype: string): string => {
    if (mimetype.startsWith('image/')) return '🖼️';
    if (mimetype === 'application/pdf') return '📄';
    if (mimetype.includes('word')) return '📝';
    if (mimetype.includes('excel') || mimetype.includes('spreadsheet')) return '📊';
    if (mimetype.includes('powerpoint') || mimetype.includes('presentation')) return '📽️';
    if (mimetype.startsWith('text/')) return '📃';
    if (mimetype.includes('zip') || mimetype.includes('rar')) return '🗜️';
    return '📎';
  }
};
