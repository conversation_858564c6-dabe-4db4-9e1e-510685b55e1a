'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import { 
  ArrowLeft, 
  Calendar, 
  Clock, 
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Pause,
  Play,
  User<PERSON>he<PERSON>,
  Eye
} from 'lucide-react';

import Button from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import Input from '@/components/ui/Input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { sessionsApi } from '@/lib/api/sessions';
import { classroomsApi } from '@/lib/api/classrooms';
import { Session, Classroom, SessionStats, CreateSessionRequest, UpdateSessionStatusRequest } from '@/types';
import SessionStatusManager from '@/components/SessionStatusManager';

export default function ClassSessionsPage() {
  const params = useParams();
  const router = useRouter();
  const classroomId = parseInt(params.id as string);

  const [classroom, setClassroom] = useState<Classroom | null>(null);
  const [sessions, setSessions] = useState<Session[]>([]);
  const [stats, setStats] = useState<SessionStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  // Dialog states
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showStatusDialog, setShowStatusDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedSession, setSelectedSession] = useState<Session | null>(null);

  // Form states
  const [formData, setFormData] = useState<CreateSessionRequest>({
    class_id: classroomId,
    date: '',
    start_time: '',
    end_time: '',
    title: '',
    description: '',
    location: ''
  });
  const [statusData, setStatusData] = useState<UpdateSessionStatusRequest>({
    status: 'scheduled',
    notes: ''
  });

  useEffect(() => {
    fetchData();
  }, [classroomId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [classroomData, sessionsData, statsData] = await Promise.all([
        classroomsApi.getClassroomById(classroomId),
        sessionsApi.getClassSessions(classroomId),
        sessionsApi.getSessionStats(classroomId)
      ]);

      setClassroom(classroomData);
      setSessions(sessionsData?.items || []);
      setStats(statsData);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Không thể tải dữ liệu');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSession = async () => {
    try {
      setActionLoading(true);
      await sessionsApi.createSession(classroomId, formData);
      toast.success('Tạo lịch học thành công');
      setShowCreateDialog(false);
      resetForm();
      fetchData();
    } catch (error) {
      console.error('Error creating session:', error);
      toast.error('Không thể tạo lịch học');
    } finally {
      setActionLoading(false);
    }
  };

  const handleStatusChange = (session: Session) => {
    setSelectedSession(session);
    setStatusData({
      status: session.status,
      notes: session.notes || ''
    });
    setShowStatusDialog(true);
  };

  const handleDeleteSession = (session: Session) => {
    setSelectedSession(session);
    setShowDeleteDialog(true);
  };

  const confirmStatusChange = async () => {
    if (!selectedSession) return;

    try {
      setActionLoading(true);
      await sessionsApi.updateSessionStatus(selectedSession.id, statusData);
      toast.success('Cập nhật trạng thái lịch học thành công');
      setShowStatusDialog(false);
      fetchData();
    } catch (error) {
      console.error('Error updating session status:', error);
      toast.error('Không thể cập nhật trạng thái lịch học');
    } finally {
      setActionLoading(false);
    }
  };

  const confirmDeleteSession = async () => {
    if (!selectedSession) return;

    try {
      setActionLoading(true);
      await sessionsApi.deleteSession(selectedSession.id);
      toast.success('Xóa lịch học thành công');
      setShowDeleteDialog(false);
      fetchData();
    } catch (error) {
      console.error('Error deleting session:', error);
      toast.error('Không thể xóa lịch học');
    } finally {
      setActionLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      class_id: classroomId,
      date: '',
      start_time: '',
      end_time: '',
      title: '',
      description: '',
      location: ''
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Đã lên lịch</Badge>;
      case 'in_progress':
        return <Badge variant="default" className="bg-green-100 text-green-800">Đang diễn ra</Badge>;
      case 'completed':
        return <Badge variant="default" className="bg-gray-100 text-gray-800">Đã hoàn thành</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">Đã hủy</Badge>;
      case 'postponed':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Hoãn lại</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Calendar className="h-4 w-4" />;
      case 'in_progress':
        return <Play className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      case 'postponed':
        return <Pause className="h-4 w-4" />;
      default:
        return <Calendar className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href={`/teacher/classes/${classroomId}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Quản lý lịch học</h1>
            <p className="text-gray-600">{classroom?.name}</p>
          </div>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Tạo lịch học
        </Button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Tổng buổi học</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalSessions}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Đã hoàn thành</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.completedSessions}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Đã lên lịch</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.scheduledSessions}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-purple-600 font-bold text-sm">{stats.completionRate}%</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Tỷ lệ hoàn thành</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.completionRate}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Session Status Manager */}
      <div className="mb-6">
        <SessionStatusManager
          classroomId={classroomId}
          showAdminControls={false}
        />
      </div>

      {/* Sessions List */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách lịch học</CardTitle>
        </CardHeader>
        <CardContent>
          {!sessions || sessions.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Chưa có lịch học nào</p>
              <Button 
                className="mt-4" 
                onClick={() => setShowCreateDialog(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Tạo lịch học đầu tiên
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {sessions.map((session) => (
                <div key={session.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        {getStatusIcon(session.status)}
                        <h3 className="font-medium text-gray-900">{session.title}</h3>
                        {getStatusBadge(session.status)}
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Ngày:</span> {new Date(session.date).toLocaleDateString('vi-VN')}
                        </div>
                        <div>
                          <span className="font-medium">Thời gian:</span> {session.start_time} - {session.end_time}
                        </div>
                        <div>
                          <span className="font-medium">Địa điểm:</span> {session.location || 'Chưa xác định'}
                        </div>
                      </div>
                      {session.description && (
                        <p className="mt-2 text-sm text-gray-600">{session.description}</p>
                      )}
                      {session.notes && (
                        <p className="mt-2 text-sm text-blue-600 bg-blue-50 p-2 rounded">
                          <span className="font-medium">Ghi chú:</span> {session.notes}
                        </p>
                      )}
                    </div>
                    <div className="ml-4">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/teacher/classes/${classroomId}/sessions/${session.id}`}>
                              <Eye className="mr-2 h-4 w-4" />
                              Xem chi tiết
                            </Link>
                          </DropdownMenuItem>

                          {session.status === 'in_progress' && (
                            <DropdownMenuItem asChild>
                              <Link href={`/teacher/classes/${classroomId}/sessions/${session.id}`}>
                                <UserCheck className="mr-2 h-4 w-4" />
                                Điểm danh
                              </Link>
                            </DropdownMenuItem>
                          )}

                          <DropdownMenuItem onClick={() => handleStatusChange(session)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Cập nhật trạng thái
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteSession(session)}
                            className="text-red-600"
                            disabled={session.status === 'completed'}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Xóa lịch học
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Session Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Tạo lịch học mới</DialogTitle>
            <DialogDescription>
              Tạo lịch học mới cho lớp {classroom?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="date">Ngày học</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="location">Địa điểm</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                  placeholder="Phòng học"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="start_time">Giờ bắt đầu</Label>
                <Input
                  id="start_time"
                  type="time"
                  value={formData.start_time}
                  onChange={(e) => setFormData({ ...formData, start_time: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="end_time">Giờ kết thúc</Label>
                <Input
                  id="end_time"
                  type="time"
                  value={formData.end_time}
                  onChange={(e) => setFormData({ ...formData, end_time: e.target.value })}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="title">Tiêu đề</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="Tên bài học"
              />
            </div>
            <div>
              <Label htmlFor="description">Mô tả</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Nội dung bài học"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Hủy
            </Button>
            <Button 
              onClick={handleCreateSession} 
              disabled={actionLoading || !formData.date || !formData.start_time || !formData.end_time}
            >
              {actionLoading ? 'Đang tạo...' : 'Tạo lịch học'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Status Change Dialog */}
      <Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cập nhật trạng thái lịch học</DialogTitle>
            <DialogDescription>
              Cập nhật trạng thái cho: {selectedSession?.title}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="status">Trạng thái</Label>
              <Select
                value={statusData.status}
                onValueChange={(value: any) => setStatusData({ ...statusData, status: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="scheduled">Đã lên lịch</SelectItem>
                  <SelectItem value="in_progress">Đang diễn ra</SelectItem>
                  <SelectItem value="completed">Đã hoàn thành</SelectItem>
                  <SelectItem value="cancelled">Đã hủy</SelectItem>
                  <SelectItem value="postponed">Hoãn lại</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="status-notes">Ghi chú</Label>
              <Textarea
                id="status-notes"
                value={statusData.notes}
                onChange={(e) => setStatusData({ ...statusData, notes: e.target.value })}
                placeholder="Ghi chú về buổi học..."
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowStatusDialog(false)}>
              Hủy
            </Button>
            <Button onClick={confirmStatusChange} disabled={actionLoading}>
              {actionLoading ? 'Đang cập nhật...' : 'Cập nhật'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Session Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xóa lịch học</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa lịch học "{selectedSession?.title}"?
              Hành động này không thể hoàn tác.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Hủy
            </Button>
            <Button variant="destructive" onClick={confirmDeleteSession} disabled={actionLoading}>
              {actionLoading ? 'Đang xóa...' : 'Xóa lịch học'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
