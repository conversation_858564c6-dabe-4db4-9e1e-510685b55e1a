import { apiClient } from './client';
import { 
  Session, 
  CreateSessionRequest, 
  UpdateSessionRequest,
  UpdateSessionStatusRequest,
  SessionStats,
  PaginatedResponse 
} from '@/types';

export const sessionsApi = {
  // Get all sessions for a classroom
  getClassSessions: async (
    classroomId: number,
    page = 0,
    limit = 20,
    status?: string
  ): Promise<PaginatedResponse<Session>> => {
    let url = `/classrooms/${classroomId}/sessions?page=${page}&limit=${limit}`;
    if (status) {
      url += `&status=${status}`;
    }
    const response = await apiClient.get(url);
    return response.data.data;
  },

  // Create new session
  createSession: async (
    classroomId: number,
    data: CreateSessionRequest
  ): Promise<Session> => {
    const response = await apiClient.post(`/classrooms/${classroomId}/sessions`, data);
    return response.data;
  },

  // Get session by ID
  getSessionById: async (sessionId: number): Promise<Session> => {
    const response = await apiClient.get(`/classrooms/sessions/${sessionId}`);
    return response.data;
  },

  // Update session details
  updateSession: async (
    sessionId: number,
    data: UpdateSessionRequest
  ): Promise<Session> => {
    const response = await apiClient.put(`/classrooms/sessions/${sessionId}`, data);
    return response.data;
  },

  // Update session status
  updateSessionStatus: async (
    sessionId: number,
    data: UpdateSessionStatusRequest
  ): Promise<Session> => {
    const response = await apiClient.put(
      `/classrooms/sessions/${sessionId}/status`,
      data
    );
    return response.data;
  },

  // Delete session
  deleteSession: async (sessionId: number): Promise<{ message: string }> => {
    const response = await apiClient.delete(`/classrooms/sessions/${sessionId}`);
    return response.data;
  },

  // Get session by ID
  getSessionById: async (sessionId: number): Promise<Session> => {
    const response = await apiClient.get(`/sessions/${sessionId}`);
    return response.data.data;
  },

  // Get session statistics for a classroom
  getSessionStats: async (classroomId: number): Promise<SessionStats> => {
    const response = await apiClient.get(`/classrooms/${classroomId}/sessions/stats`);
    return response.data.data;
  },
};
