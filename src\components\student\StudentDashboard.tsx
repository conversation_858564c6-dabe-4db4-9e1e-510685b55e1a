'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { BookOpen, Clock, CheckCircle, XCircle, AlertCircle, GraduationCap } from 'lucide-react';
import Link from 'next/link';
import Button from '@/components/ui/Button';
import { enrollmentsApi } from '@/lib/api/enrollments';

interface DashboardStats {
  totalEnrollments: number;
  pendingEnrollments: number;
  approvedEnrollments: number;
  rejectedEnrollments: number;
}

export default function StudentDashboard() {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalEnrollments: 0,
    pendingEnrollments: 0,
    approvedEnrollments: 0,
    rejectedEnrollments: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Get student enrollments to calculate stats
        const enrollments = await enrollmentsApi.getStudentEnrollments(0, 100);

        const totalEnrollments = enrollments.total || 0;
        const pendingEnrollments = enrollments.items?.filter((e: any) => e.status === 'pending').length || 0;
        const approvedEnrollments = enrollments.items?.filter((e: any) => e.status === 'approved').length || 0;
        const rejectedEnrollments = enrollments.items?.filter((e: any) => e.status === 'rejected').length || 0;

        setStats({
          totalEnrollments,
          pendingEnrollments,
          approvedEnrollments,
          rejectedEnrollments,
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
        // Use default stats on error
        setStats({
          totalEnrollments: 0,
          pendingEnrollments: 0,
          approvedEnrollments: 0,
          rejectedEnrollments: 0,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          Chào mừng, {user?.name}! 👋
        </h1>
        <p className="text-blue-100 mb-4">
          {user?.grade ? `Học sinh lớp ${user.grade}` : 'Chưa thiết lập lớp học'} • 
          Hôm nay là ngày tuyệt vời để học tập!
        </p>
        {!user?.grade && (
          <div className="bg-yellow-500 bg-opacity-20 border border-yellow-300 rounded-md p-3 mb-4">
            <p className="text-yellow-100 text-sm">
              ⚠️ Bạn chưa thiết lập lớp học. Vui lòng liên hệ quản trị viên để cập nhật thông tin.
            </p>
          </div>
        )}
        <div className="flex flex-wrap gap-3">
          <Link href="/student/classes">
            <Button variant="outline" className="bg-white text-blue-600 hover:bg-gray-50">
              <BookOpen className="mr-2 h-4 w-4" />
              Tìm lớp học
            </Button>
          </Link>
          <Link href="/student/enrollments">
            <Button variant="outline" className="bg-white text-blue-600 hover:bg-gray-50">
              <GraduationCap className="mr-2 h-4 w-4" />
              Đăng ký của tôi
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Tổng đăng ký</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalEnrollments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Chờ duyệt</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pendingEnrollments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Đã duyệt</p>
                <p className="text-2xl font-bold text-gray-900">{stats.approvedEnrollments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Bị từ chối</p>
                <p className="text-2xl font-bold text-gray-900">{stats.rejectedEnrollments}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="mr-2 h-5 w-5" />
              Tìm lớp học
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Khám phá các lớp học phù hợp với cấp độ của bạn và đăng ký ngay hôm nay.
            </p>
            <Link href="/student/classes">
              <Button className="w-full">
                Xem lớp học có sẵn
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <GraduationCap className="mr-2 h-5 w-5" />
              Quản lý đăng ký
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Theo dõi trạng thái đăng ký và quản lý các lớp học bạn đã tham gia.
            </p>
            <Link href="/student/enrollments">
              <Button variant="outline" className="w-full">
                Xem đăng ký của tôi
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Hoạt động gần đây</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stats.totalEnrollments > 0 ? (
              <div className="text-center py-8">
                <AlertCircle className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Chưa có hoạt động</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Các hoạt động đăng ký và học tập sẽ hiển thị ở đây.
                </p>
              </div>
            ) : (
              <div className="text-center py-8">
                <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Chưa có đăng ký nào</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Hãy bắt đầu bằng cách tìm và đăng ký lớp học phù hợp.
                </p>
                <div className="mt-6">
                  <Link href="/student/classes">
                    <Button>
                      <BookOpen className="mr-2 h-4 w-4" />
                      Tìm lớp học
                    </Button>
                  </Link>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Tips Section */}
      <Card>
        <CardHeader>
          <CardTitle>💡 Mẹo học tập</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Đăng ký sớm</h4>
              <p className="text-sm text-blue-700">
                Đăng ký lớp học sớm để đảm bảo có chỗ và có thời gian chuẩn bị tốt nhất.
              </p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-medium text-green-900 mb-2">Tham gia tích cực</h4>
              <p className="text-sm text-green-700">
                Tham gia đầy đủ các buổi học và hoàn thành bài tập để đạt kết quả tốt nhất.
              </p>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <h4 className="font-medium text-purple-900 mb-2">Liên hệ giáo viên</h4>
              <p className="text-sm text-purple-700">
                Đừng ngại liên hệ với giáo viên khi có thắc mắc hoặc cần hỗ trợ.
              </p>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg">
              <h4 className="font-medium text-orange-900 mb-2">Theo dõi tiến độ</h4>
              <p className="text-sm text-orange-700">
                Thường xuyên kiểm tra tiến độ học tập và điểm số để cải thiện.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
