'use client';

import React, { useState } from 'react';
import { Assignment, assignmentsApi, FileInfo } from '@/lib/api/assignments';
import { useAuth } from '@/context/AuthContext';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import SimpleFileUpload from './SimpleFileUpload';
import FilePreview from './FilePreview';
import { Upload, Download, FileText, CheckCircle, Clock, AlertCircle, Star } from 'lucide-react';
import toast from 'react-hot-toast';

interface StudentSubmissionProps {
  assignment: Assignment;
  onUpdate?: () => void;
}

export default function StudentSubmission({ assignment, onUpdate }: StudentSubmissionProps) {
  const { user } = useAuth();
  const [submitting, setSubmitting] = useState(false);
  const [showUpload, setShowUpload] = useState(false);

  const isStudent = user?.role === 'student';
  const submission = assignment.user_submission;
  const canSubmit = assignment.can_submit && isStudent;
  const hasSubmitted = !!submission;

  const handleSubmitAssignment = async (files: File[]) => {
    if (!isStudent || !canSubmit) return;

    try {
      setSubmitting(true);
      await assignmentsApi.submitAssignment(assignment.id, files);
      toast.success('Đã nộp bài thành công!');
      setShowUpload(false);
      onUpdate?.();
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Lỗi khi nộp bài');
    } finally {
      setSubmitting(false);
    }
  };



  const getStatusIcon = () => {
    if (!hasSubmitted) {
      return canSubmit ? <Clock className="h-5 w-5 text-yellow-600" /> : <AlertCircle className="h-5 w-5 text-red-600" />;
    }
    return <CheckCircle className="h-5 w-5 text-green-600" />;
  };

  const getStatusText = () => {
    if (!hasSubmitted) {
      return canSubmit ? 'Chưa nộp bài' : 'Hết hạn nộp bài';
    }
    return submission?.is_late ? 'Đã nộp (muộn)' : 'Đã nộp bài';
  };

  const getStatusColor = () => {
    if (!hasSubmitted) {
      return canSubmit ? 'text-yellow-600' : 'text-red-600';
    }
    return submission?.is_late ? 'text-orange-600' : 'text-green-600';
  };

  if (!isStudent) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            {getStatusIcon()}
            <span className="ml-2">Bài nộp của tôi</span>
          </CardTitle>
          
          <div className="flex items-center gap-3">
            <span className={`text-sm font-medium ${getStatusColor()}`}>
              {getStatusText()}
            </span>
            
            {canSubmit && !hasSubmitted && (
              <Button
                onClick={() => setShowUpload(true)}
                size="sm"
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Upload className="mr-2 h-4 w-4" />
                Nộp bài
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Upload Section */}
        {canSubmit && !hasSubmitted && showUpload && (
          <div className="mb-6 p-4 border border-blue-200 rounded-lg bg-blue-50">
            <h4 className="font-medium text-blue-900 mb-3">Nộp bài tập</h4>
            <p className="text-sm text-blue-700 mb-3">
              Hạn nộp: {new Date(assignment.deadline).toLocaleString()}
            </p>
            <SimpleFileUpload
              onFilesSelected={handleSubmitAssignment}
              uploading={submitting}
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.zip,.rar"
              maxFiles={5}
              maxSize={20 * 1024 * 1024} // 20MB
            />
            <div className="flex gap-2 mt-3">
              <Button
                onClick={() => setShowUpload(false)}
                variant="outline"
                size="sm"
              >
                Hủy
              </Button>
            </div>
          </div>
        )}

        {/* Submission Display */}
        {hasSubmitted ? (
          <div className="space-y-4">
            {/* Submission Info */}
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div>
                <p className="font-medium text-green-800">
                  Đã nộp bài {submission?.is_late && '(muộn)'}
                </p>
                <p className="text-sm text-green-600">
                  Thời gian nộp: {new Date(submission?.submitted_at || '').toLocaleString()}
                </p>
              </div>
              
              {submission?.score !== null && submission?.score !== undefined && (
                <div className="text-right">
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-500 mr-1" />
                    <span className="font-bold text-lg text-gray-900">
                      {submission.score}/{assignment.max_score}
                    </span>
                  </div>
                  <p className="text-sm text-gray-500">
                    {((submission.score / assignment.max_score) * 100).toFixed(1)}%
                  </p>
                </div>
              )}
            </div>

            {/* Submitted Files */}
            {submission?.submission_files && submission.submission_files.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-3">File đã nộp:</h4>
                <div className="space-y-3">
                  {submission.submission_files.map((file: any, index: number) => (
                    <FilePreview key={index} file={file} showPreview={true} />
                  ))}
                </div>
              </div>
            )}

            {/* Teacher Feedback */}
            {submission?.feedback && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Nhận xét của giáo viên:</h4>
                <p className="text-gray-700 whitespace-pre-wrap">{submission.feedback}</p>
              </div>
            )}
          </div>
        ) : canSubmit ? (
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Chưa nộp bài</h3>
            <p className="mt-1 text-sm text-gray-500">
              Hạn nộp: {new Date(assignment.deadline).toLocaleString()}
            </p>
            <div className="mt-4">
              <Button
                onClick={() => setShowUpload(true)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Upload className="mr-2 h-4 w-4" />
                Nộp bài ngay
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <AlertCircle className="mx-auto h-12 w-12 text-red-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Đã hết hạn nộp bài</h3>
            <p className="mt-1 text-sm text-gray-500">
              Hạn nộp đã kết thúc vào {new Date(assignment.deadline).toLocaleString()}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
