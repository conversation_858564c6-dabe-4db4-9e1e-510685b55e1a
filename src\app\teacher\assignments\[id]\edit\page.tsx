'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { EditAssignmentForm } from '@/components/assignments';
import { assignmentsApi } from '@/lib/api/assignments';
import Link from 'next/link';

export default function EditAssignmentPage() {
  const params = useParams();
  const router = useRouter();
  const assignmentId = parseInt(params.id as string);
  const { user } = useAuth();
  const [assignment, setAssignment] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAssignment();
  }, [assignmentId]);

  const fetchAssignment = async () => {
    try {
      setLoading(true);
      const data = await assignmentsApi.getAssignment(assignmentId);
      setAssignment(data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch assignment');
    } finally {
      setLoading(false);
    }
  };

  const handleSuccess = () => {
    router.push(`/teacher/assignments/${assignmentId}`);
  };

  const handleCancel = () => {
    router.push(`/teacher/assignments/${assignmentId}`);
  };

  // Redirect if not teacher
  if (user?.role !== 'teacher') {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600">Access denied. Only teachers can edit assignments.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !assignment) {
    return (
      <div className="space-y-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error || 'Assignment not found'}</p>
        </div>
        <Link
          href={`/teacher/assignments/${assignmentId}`}
          className="text-blue-600 hover:text-blue-800 underline"
        >
          ← Back to Assignment
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-gray-600">
        <Link href="/teacher/classes" className="hover:text-blue-600">
          Classes
        </Link>
        <span>›</span>
        <Link href={`/teacher/classes/${assignment.classroom_id}`} className="hover:text-blue-600">
          {assignment.classroom_name}
        </Link>
        <span>›</span>
        <Link href={`/teacher/classes/${assignment.classroom_id}/assignments`} className="hover:text-blue-600">
          Assignments
        </Link>
        <span>›</span>
        <Link href={`/teacher/assignments/${assignmentId}`} className="hover:text-blue-600">
          {assignment.title}
        </Link>
        <span>›</span>
        <span className="text-gray-900">Edit</span>
      </nav>

      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Assignment</h1>
            <p className="text-gray-600 mt-1">
              {assignment.classroom_name} • {assignment.title}
            </p>
          </div>
          <Link
            href={`/teacher/assignments/${assignmentId}`}
            className="text-gray-600 hover:text-gray-800"
          >
            ← Back to Assignment
          </Link>
        </div>
      </div>

      {/* Edit Form */}
      <EditAssignmentForm
        assignmentId={assignmentId}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}
