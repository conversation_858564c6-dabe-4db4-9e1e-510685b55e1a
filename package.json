{"name": "front-end", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 9006", "start:production": "NODE_ENV=production next start -p 9006", "lint": "next lint", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:stop": "pm2 stop cms-frontend", "pm2:restart": "pm2 restart cms-frontend", "pm2:delete": "pm2 delete cms-frontend", "pm2:logs": "pm2 logs cms-frontend", "pm2:status": "pm2 status"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}