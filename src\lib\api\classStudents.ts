import { apiClient } from './client';
import { 
  ClassStudent, 
  UpdateStudentStatusRequest, 
  ClassStudentStats,
  PaginatedResponse 
} from '@/types';

export const classStudentsApi = {
  // Get all students in a classroom
  getClassStudents: async (
    classroomId: number,
    page = 0,
    limit = 20
  ): Promise<PaginatedResponse<ClassStudent>> => {
    const response = await apiClient.get(
      `/classrooms/${classroomId}/students?page=${page}&limit=${limit}`
    );
    return response.data.data;
  },

  // Update student status in class
  updateStudentStatus: async (
    classroomId: number,
    studentId: number,
    data: UpdateStudentStatusRequest
  ): Promise<ClassStudent> => {
    const response = await apiClient.put(
      `/classrooms/${classroomId}/students/${studentId}/status`,
      data
    );
    return response.data;
  },

  // Remove student from class
  removeStudentFromClass: async (
    classroomId: number,
    studentId: number,
    notes?: string
  ): Promise<ClassStudent> => {
    const response = await apiClient.delete(
      `/classrooms/${classroomId}/students/${studentId}`,
      { data: { notes } }
    );
    return response.data;
  },

  // Get student statistics for a classroom
  getClassStudentStats: async (classroomId: number): Promise<ClassStudentStats> => {
    const response = await apiClient.get(`/classrooms/${classroomId}/students/stats`);
    return response.data.data;
  },

  // Get student details in class
  getStudentInClass: async (
    classroomId: number,
    studentId: number
  ): Promise<ClassStudent> => {
    const response = await apiClient.get(
      `/classrooms/${classroomId}/students/${studentId}`
    );
    return response.data;
  },
};
