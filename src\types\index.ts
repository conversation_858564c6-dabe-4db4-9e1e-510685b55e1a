// User types
export interface User {
  id: number;
  name: string;
  email: string;
  role: 'admin' | 'teacher' | 'student';
  grade?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Auth types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  role?: 'admin' | 'teacher' | 'student';
  grade?: number;
}

export interface AuthResponse {
  user: User;
  token: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

// Course types
export interface Course {
  id: number;
  name: string;
  description?: string;
  start_date: string;
  end_date: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateCourseRequest {
  name: string;
  description?: string;
  start_date: string;
  end_date: string;
}

export interface UpdateCourseRequest {
  name: string;
  description?: string;
  start_date: string;
  end_date: string;
}

// Classroom types
export interface Classroom {
  id: number;
  name: string;
  description?: string;
  course_id: number;
  teacher_id: number;
  grade: number;
  start_date?: string;
  end_date?: string;
  fee_per_session: string;
  max_students: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  enrolled_count?: number; // Number of students actually enrolled
  course?: Course;
  teacher?: User;
  students?: ClassStudent[];
}

export interface CreateClassroomRequest {
  name: string;
  description?: string;
  course_id: number;
  grade: number;
  start_date?: string;
  end_date?: string;
  fee_per_session: number;
  max_students: number;
}

export interface UpdateClassroomRequest {
  name: string;
  description?: string;
  course_id: number;
  grade: number;
  start_date?: string;
  end_date?: string;
  fee_per_session: number;
  max_students: number;
}

// Enrollment types
export interface Enrollment {
  id: number;
  student_id: number;
  classroom_id: number;
  status: 'pending' | 'approved' | 'rejected';
  enrolled_at: string;
  approved_at?: string;
  approved_by?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  student?: User;
  classroom?: Classroom;
  approvedBy?: User;
}

export interface EnrollmentRequest {
  classroom_id: number;
}

export interface UpdateEnrollmentStatusRequest {
  status: 'approved' | 'rejected';
  notes?: string;
}

// ClassStudent types (legacy)
export interface ClassStudent {
  id: number;
  class_id: number;
  student_id: number;
  status: 'pending' | 'approved' | 'rejected';
  student_status: 'active' | 'suspended' | 'withdrawn';
  notes?: string;
  teacher_notes?: string;
  joined_at: string;
  approved_at?: string;
  approved_by?: number;
  student?: User;
  classroom?: Classroom;
}

export interface EnrollRequest {
  class_id: number;
  notes?: string;
}

export interface UpdateStudentEnrollmentStatusRequest {
  status: 'approved' | 'rejected';
  teacher_notes?: string;
}

// Session types
export interface Session {
  id: number;
  class_id: number;
  date: string;
  start_time: string;
  end_time: string;
  title: string;
  description?: string;
  location?: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'postponed';
  notes?: string;
  created_by: number;
  created_at: string;
  updated_at: string;
  classroom?: Classroom;
  teacher?: User;
}

export interface CreateSessionRequest {
  class_id: number;
  date: string;
  start_time: string;
  end_time: string;
  title?: string;
  description?: string;
  location?: string;
}

export interface UpdateSessionRequest {
  date?: string;
  start_time?: string;
  end_time?: string;
  title?: string;
  description?: string;
  location?: string;
}

export interface UpdateSessionStatusRequest {
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'postponed';
  notes?: string;
}

export interface UpdateStudentStatusRequest {
  status: 'active' | 'suspended' | 'withdrawn';
  notes?: string;
}

// Statistics types
export interface ClassStudentStats {
  totalStudents: number;
  activeStudents: number;
  suspendedStudents: number;
  withdrawnStudents: number;
  maxStudents: number;
  availableSlots: number;
}

export interface SessionStats {
  totalSessions: number;
  completedSessions: number;
  scheduledSessions: number;
  cancelledSessions: number;
  completionRate: number;
}



// Attendance types
export interface Attendance {
  id: number;
  session_id: number;
  student_id: number;
  status: 'present' | 'absent';
  notes?: string;
  marked_at: string;
  marked_by?: number;
  session?: Session;
  student?: User;
  markedBy?: User;
}

export interface MarkAttendanceRequest {
  session_id: number;
  attendances: Array<{
    student_id: number;
    status: 'present' | 'absent';
    notes?: string;
  }>;
}

// Tuition types
export interface TuitionLog {
  id: number;
  class_id: number;
  calculated_by: number;
  from_date: string;
  to_date: string;
  total_sessions: number;
  total_students: number;
  total_amount: string;
  notes?: string;
  calculated_at: string;
  classroom?: Classroom;
  calculatedBy?: User;
  tuitionDetails?: TuitionDetail[];
}

export interface TuitionDetail {
  id: number;
  tuition_log_id: number;
  student_id: number;
  total_sessions: number;
  amount_per_session: string;
  total_amount: string;
  payment_status: 'pending' | 'paid' | 'overdue';
  payment_date?: string;
  payment_notes?: string;
  created_at: string;
  updated_at: string;
  tuitionLog?: TuitionLog;
  student?: User;
}

export interface CalculateTuitionRequest {
  class_id: number;
  from_date: string;
  to_date: string;
  notes?: string;
}

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

// Teacher management types (Admin)
export interface CreateTeacherRequest {
  name: string;
  email: string;
  password: string;
}

export interface Course {
  id: number;
  name: string;
  description?: string;
  start_date: string;
  end_date: string;
  created_by: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}



export interface TeacherWithStats extends User {
  total_classes?: number;
  active_classes?: number;
  classes?: Classroom[];
}

// Form types
export interface FormErrors {
  [key: string]: string | number;
}

// Dashboard stats
export interface AdminStats {
  total_teachers: number;
  active_teachers: number;
  inactive_teachers: number;
}

export interface TeacherStats {
  total_classes: number;
  active_classes: number;
  total_students: number;
  pending_enrollments: number;
}

export interface StudentStats {
  enrolled_classes: number;
  pending_enrollments: number;
  total_sessions: number;
  attendance_rate: number;
}
