'use client';

import React, { useState, useRef } from 'react';
import { assignmentsApi } from '@/lib/api/assignments';

interface FileUploadProps {
  assignmentId: number;
  uploadType: 'question' | 'solution' | 'submission';
  onSuccess?: () => void;
  onCancel?: () => void;
}

export default function FileUpload({ assignmentId, uploadType, onSuccess, onCancel }: FileUploadProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const allowedTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'application/zip',
    'application/x-rar-compressed'
  ];

  const maxFileSize = 50 * 1024 * 1024; // 50MB
  const maxFiles = 10;

  const validateFile = (file: File): string | null => {
    if (!allowedTypes.includes(file.type)) {
      return `File type "${file.type}" is not allowed`;
    }
    if (file.size > maxFileSize) {
      return `File "${file.name}" is too large (max 50MB)`;
    }
    return null;
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const fileArray = Array.from(files);
    const errors: string[] = [];

    // Check total file count
    if (selectedFiles.length + fileArray.length > maxFiles) {
      setError(`Maximum ${maxFiles} files allowed`);
      return;
    }

    // Validate each file
    const validFiles: File[] = [];
    fileArray.forEach(file => {
      const error = validateFile(file);
      if (error) {
        errors.push(error);
      } else {
        validFiles.push(file);
      }
    });

    if (errors.length > 0) {
      setError(errors.join(', '));
      return;
    }

    setSelectedFiles(prev => [...prev, ...validFiles]);
    setError(null);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      setError('Please select at least one file');
      return;
    }

    try {
      setUploading(true);
      setError(null);

      if (uploadType === 'question') {
        await assignmentsApi.uploadQuestionFiles(assignmentId, selectedFiles);
      } else if (uploadType === 'solution') {
        await assignmentsApi.uploadSolutionFiles(assignmentId, selectedFiles);
      } else {
        await assignmentsApi.submitAssignment(assignmentId, selectedFiles);
      }

      setSelectedFiles([]);
      if (onSuccess) onSuccess();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Upload failed');
    } finally {
      setUploading(false);
    }
  };

  const getFileIcon = (file: File) => {
    return assignmentsApi.getFileTypeIcon(file.type);
  };

  const formatFileSize = (bytes: number) => {
    return assignmentsApi.formatFileSize(bytes);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-gray-900">
          {uploadType === 'question' ? 'Upload Question Files' :
           uploadType === 'solution' ? 'Upload Solution Files' : 'Submit Your Work'}
        </h3>
        {onCancel && (
          <button
            onClick={onCancel}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* File Drop Zone */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragOver
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <div className="space-y-2">
          <div className="text-4xl">📁</div>
          <div>
            <p className="text-gray-600">
              Drop files here or <span className="text-blue-600 underline cursor-pointer">browse</span>
            </p>
            <p className="text-sm text-gray-500 mt-1">
              Supported: Images, PDF, Word, Excel, PowerPoint, Text, ZIP (Max 50MB each, {maxFiles} files total)
            </p>
          </div>
        </div>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={allowedTypes.join(',')}
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
      />

      {/* Selected Files */}
      {selectedFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-medium text-gray-900">Selected Files ({selectedFiles.length})</h4>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {selectedFiles.map((file, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center gap-2">
                  <span className="text-lg">{getFileIcon(file)}</span>
                  <div>
                    <p className="font-medium text-sm">{file.name}</p>
                    <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                  </div>
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="text-red-600 hover:text-red-800 text-sm"
                  disabled={uploading}
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upload Button */}
      {selectedFiles.length > 0 && (
        <div className="flex justify-end gap-3">
          {onCancel && (
            <button
              onClick={onCancel}
              disabled={uploading}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 disabled:opacity-50 transition-colors"
            >
              Cancel
            </button>
          )}
          <button
            onClick={handleUpload}
            disabled={uploading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {uploading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Uploading...
              </div>
            ) : (
              uploadType === 'solution' ? 'Upload Solution' : 'Submit Assignment'
            )}
          </button>
        </div>
      )}

      {/* Upload Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <p className="text-blue-800 text-sm">
          <strong>Instructions:</strong>
        </p>
        <ul className="text-blue-700 text-sm mt-1 space-y-1">
          {uploadType === 'question' ? (
            <>
              <li>• Upload question files (PDF, images, documents) for students to view</li>
              <li>• Students can access these files immediately after you upload them</li>
              <li>• PDF files will be downloadable, images will be viewable directly</li>
            </>
          ) : uploadType === 'solution' ? (
            <>
              <li>• Upload solution files for students to view after the deadline</li>
              <li>• Students can only access these files after the assignment deadline</li>
              <li>• You can upload multiple files and delete them later if needed</li>
            </>
          ) : (
            <>
              <li>• Upload your completed assignment files</li>
              <li>• Make sure all required files are included</li>
              <li>• You can only submit once, so double-check your work</li>
              <li>• Submission must be before the deadline</li>
            </>
          )}
        </ul>
      </div>
    </div>
  );
}
