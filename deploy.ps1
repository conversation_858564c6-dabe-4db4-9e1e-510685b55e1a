# 🚀 Frontend Production Deployment Script for Windows
# Usage: .\deploy.ps1

Write-Host "🚀 Starting Frontend Production Deployment..." -ForegroundColor Blue

function Write-Status {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Cyan
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if PM2 is installed
Write-Status "Checking PM2 installation..."
try {
    pm2 --version | Out-Null
    Write-Success "PM2 is installed"
} catch {
    Write-Error "PM2 is not installed. Installing PM2..."
    npm install -g pm2
    if ($LASTEXITCODE -eq 0) {
        Write-Success "PM2 installed successfully"
    } else {
        Write-Error "Failed to install PM2"
        exit 1
    }
}

# Check if backend is running
Write-Status "Checking backend connection..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:9005/health" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Success "Backend is running on port 9005"
    }
} catch {
    Write-Warning "Backend is not responding on port 9005"
    Write-Warning "Make sure backend is running before starting frontend"
}

# Install dependencies
Write-Status "Installing dependencies..."
npm install
if ($LASTEXITCODE -eq 0) {
    Write-Success "Dependencies installed"
} else {
    Write-Error "Failed to install dependencies"
    exit 1
}

# Create environment file if not exists
if (-not (Test-Path ".env.local")) {
    Write-Status "Creating .env.local from template..."
    Copy-Item ".env.production" ".env.local"
    Write-Success ".env.local created"
} else {
    Write-Status ".env.local already exists"
}

# Build the application
Write-Status "Building Next.js application..."
npm run build
if ($LASTEXITCODE -eq 0) {
    Write-Success "Build completed successfully"
} else {
    Write-Error "Build failed"
    exit 1
}

# Stop existing PM2 process if running
Write-Status "Stopping existing PM2 process..."
pm2 stop cms-frontend 2>$null
pm2 delete cms-frontend 2>$null

# Start with PM2
Write-Status "Starting application with PM2..."
npm run pm2:start
if ($LASTEXITCODE -eq 0) {
    Write-Success "Application started with PM2"
} else {
    Write-Error "Failed to start with PM2"
    exit 1
}

# Wait a moment for the app to start
Write-Status "Waiting for application to start..."
Start-Sleep -Seconds 3

# Check if application is running
Write-Status "Checking application status..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:9006" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Success "Frontend is running on http://localhost:9006"
    }
} catch {
    Write-Error "Frontend is not responding on port 9006"
    Write-Status "Checking PM2 logs..."
    pm2 logs cms-frontend --lines 10
    exit 1
}

# Show PM2 status
Write-Status "PM2 Status:"
pm2 status

Write-Success "🎉 Deployment completed successfully!"
Write-Status "Frontend URL: http://localhost:9006"
Write-Status "Backend API: http://localhost:9005/api"
Write-Status ""
Write-Status "Useful commands:"
Write-Status "  npm run pm2:logs    - View logs"
Write-Status "  npm run pm2:restart - Restart app"
Write-Status "  npm run pm2:stop    - Stop app"
Write-Status "  npm run pm2:status  - Check status"
