'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { TeacherWithStats } from '@/types';
import { validateEmail } from '@/lib/utils';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/Card';
import { apiClient } from '@/lib/api/client';
import toast from 'react-hot-toast';
import { ArrowLeft, Save, User } from 'lucide-react';
import Link from 'next/link';

export default function EditTeacherPage() {
  const router = useRouter();
  const params = useParams();
  const { isAdmin } = useAuth();
  const teacherId = parseInt(params.id as string);
  
  const [teacher, setTeacher] = useState<TeacherWithStats | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    is_active: true,
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Partial<typeof formData>>({});

  // Redirect if not admin
  useEffect(() => {
    if (!isAdmin) {
      router.push('/dashboard');
    }
  }, [isAdmin, router]);

  // Fetch teacher data
  useEffect(() => {
    if (isAdmin && teacherId) {
      fetchTeacher();
    }
  }, [isAdmin, teacherId]);

  const fetchTeacher = async () => {
    try {
      setLoading(true);
      const data = await apiClient.getTeacherById(teacherId);
      setTeacher(data);
      setFormData({
        name: data.name,
        email: data.email,
        is_active: data.is_active,
      });
    } catch (error) {
      console.error('Error fetching teacher:', error);
      toast.error('Không thể tải thông tin giáo viên');
      router.push('/admin/teachers');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const finalValue = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;
    
    setFormData(prev => ({ ...prev, [name]: finalValue }));
    
    // Clear error when user starts typing
    if (errors[name as keyof typeof formData]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<typeof formData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Họ tên là bắt buộc';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Họ tên phải có ít nhất 2 ký tự';
    }

    if (!formData.email) {
      newErrors.email = 'Email là bắt buộc';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Email không hợp lệ';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setSaving(true);
      await apiClient.updateTeacher(teacherId, formData);
      toast.success('Cập nhật thông tin giáo viên thành công!');
      router.push('/admin/teachers');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Cập nhật thất bại';
      toast.error(message);
    } finally {
      setSaving(false);
    }
  };

  if (!isAdmin) {
    return null;
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Đang tải thông tin giáo viên...</span>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/admin/teachers">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Chỉnh sửa giáo viên</h1>
            <p className="text-gray-600">Cập nhật thông tin giáo viên: {teacher?.name}</p>
          </div>
        </div>

        {/* Form */}
        <Card>
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <div className="flex items-center">
                <User className="mr-2 h-5 w-5" />
                <CardTitle>Thông tin giáo viên</CardTitle>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <Input
                label="Họ và tên"
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                error={errors.name}
                placeholder="Nguyễn Văn A"
                required
              />

              <Input
                label="Email"
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                error={errors.email}
                placeholder="<EMAIL>"
                helperText="Email sẽ được sử dụng để đăng nhập"
                required
              />

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Trạng thái tài khoản
                </label>
                <select
                  name="is_active"
                  value={formData.is_active.toString()}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.value === 'true' }))}
                  className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="true">Hoạt động</option>
                  <option value="false">Vô hiệu hóa</option>
                </select>
                <p className="text-sm text-gray-500">
                  Tài khoản vô hiệu hóa sẽ không thể đăng nhập vào hệ thống
                </p>
              </div>

              {teacher && (
                <div className="bg-gray-50 p-4 rounded-md space-y-2">
                  <h4 className="font-medium text-gray-900">Thông tin bổ sung</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">ID:</span>
                      <span className="ml-2 font-medium">{teacher.id}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Vai trò:</span>
                      <span className="ml-2 font-medium">Giáo viên</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Ngày tham gia:</span>
                      <span className="ml-2 font-medium">
                        {new Date(teacher.created_at).toLocaleDateString('vi-VN')}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">Lần cập nhật cuối:</span>
                      <span className="ml-2 font-medium">
                        {new Date(teacher.updated_at).toLocaleDateString('vi-VN')}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>

            <CardFooter className="flex justify-end space-x-4">
              <Link href="/admin/teachers">
                <Button variant="outline" disabled={saving}>
                  Hủy
                </Button>
              </Link>
              <Button
                type="submit"
                loading={saving}
                disabled={saving}
              >
                <Save className="mr-2 h-4 w-4" />
                {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
              </Button>
            </CardFooter>
          </form>
        </Card>

        {/* Security Note */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Lưu ý bảo mật</CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-gray-600 space-y-2">
            <p>• Thay đổi email sẽ ảnh hưởng đến việc đăng nhập của giáo viên</p>
            <p>• Vô hiệu hóa tài khoản sẽ ngăn giáo viên truy cập vào hệ thống</p>
            <p>• Giáo viên có thể thay đổi mật khẩu trong phần cài đặt cá nhân</p>
            <p>• Mọi thay đổi sẽ có hiệu lực ngay lập tức</p>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
