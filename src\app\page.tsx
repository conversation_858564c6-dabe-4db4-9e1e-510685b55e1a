'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';
import { GraduationCap, Users, BookOpen, Calendar } from 'lucide-react';
import Button from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';

export default function Home() {
  const { isAuthenticated, loading } = useAuth();

  // NO REDIRECTS - just show content based on auth state

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // If authenticated, show dashboard link
  if (isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Welcome back!</h1>
          <a href="/dashboard" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            Go to Dashboard
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <GraduationCap className="h-8 w-8 text-blue-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">SMS</span>
            </div>
            <div className="flex space-x-4">
              <Link href="/login">
                <Button variant="outline">Đăng nhập</Button>
              </Link>
              <Link href="/register">
                <Button>Đăng ký</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
            Hệ thống quản lý
            <span className="text-blue-600"> học sinh</span>
          </h1>
          <p className="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
            Giải pháp toàn diện cho việc quản lý lớp học, điểm danh, học phí và báo cáo.
            Dành cho giáo viên và học sinh.
          </p>
          <div className="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">
            <div className="rounded-md shadow">
              <Link href="/register">
                <Button size="lg" className="w-full sm:w-auto">
                  Bắt đầu ngay
                </Button>
              </Link>
            </div>
            <div className="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">
              <Link href="/login">
                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                  Đăng nhập
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="mt-20">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900">
              Tính năng chính
            </h2>
            <p className="mt-4 text-lg text-gray-500">
              Tất cả những gì bạn cần để quản lý lớp học hiệu quả
            </p>
          </div>

          <div className="mt-12 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <Users className="h-8 w-8 text-blue-600" />
                  <CardTitle className="ml-3">Quản lý học sinh</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500">
                  Duyệt đăng ký, quản lý danh sách học sinh trong lớp, theo dõi thông tin cá nhân.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <Calendar className="h-8 w-8 text-green-600" />
                  <CardTitle className="ml-3">Lịch học & Điểm danh</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500">
                  Tạo lịch học, điểm danh học sinh, theo dõi tỷ lệ tham gia của từng học sinh.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <BookOpen className="h-8 w-8 text-purple-600" />
                  <CardTitle className="ml-3">Học phí & Báo cáo</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500">
                  Tính toán học phí tự động, xuất báo cáo Excel, theo dõi thanh toán.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
