#!/bin/bash

# 🚀 Frontend Production Deployment Script
# Usage: ./deploy.sh

echo "🚀 Starting Frontend Production Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    print_error "PM2 is not installed. Installing PM2..."
    npm install -g pm2
    if [ $? -eq 0 ]; then
        print_success "PM2 installed successfully"
    else
        print_error "Failed to install PM2"
        exit 1
    fi
fi

# Check if backend is running
print_status "Checking backend connection..."
if curl -s http://localhost:9005/health > /dev/null; then
    print_success "Backend is running on port 9005"
else
    print_warning "Backend is not responding on port 9005"
    print_warning "Make sure backend is running before starting frontend"
fi

# Install dependencies
print_status "Installing dependencies..."
npm install
if [ $? -eq 0 ]; then
    print_success "Dependencies installed"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Create environment file if not exists
if [ ! -f .env.local ]; then
    print_status "Creating .env.local from template..."
    cp .env.production .env.local
    print_success ".env.local created"
else
    print_status ".env.local already exists"
fi

# Build the application
print_status "Building Next.js application..."
npm run build
if [ $? -eq 0 ]; then
    print_success "Build completed successfully"
else
    print_error "Build failed"
    exit 1
fi

# Stop existing PM2 process if running
print_status "Stopping existing PM2 process..."
pm2 stop cms-frontend 2>/dev/null || true
pm2 delete cms-frontend 2>/dev/null || true

# Start with PM2
print_status "Starting application with PM2..."
npm run pm2:start
if [ $? -eq 0 ]; then
    print_success "Application started with PM2"
else
    print_error "Failed to start with PM2"
    exit 1
fi

# Wait a moment for the app to start
sleep 3

# Check if application is running
print_status "Checking application status..."
if curl -s http://localhost:9006 > /dev/null; then
    print_success "Frontend is running on http://localhost:9006"
else
    print_error "Frontend is not responding on port 9006"
    print_status "Checking PM2 logs..."
    pm2 logs cms-frontend --lines 10
    exit 1
fi

# Show PM2 status
print_status "PM2 Status:"
pm2 status

print_success "🎉 Deployment completed successfully!"
print_status "Frontend URL: http://localhost:9006"
print_status "Backend API: http://localhost:9005/api"
print_status ""
print_status "Useful commands:"
print_status "  npm run pm2:logs    - View logs"
print_status "  npm run pm2:restart - Restart app"
print_status "  npm run pm2:stop    - Stop app"
print_status "  npm run pm2:status  - Check status"
