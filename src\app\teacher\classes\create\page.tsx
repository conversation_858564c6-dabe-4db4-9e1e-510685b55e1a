'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { CreateClassroomRequest, Course } from '@/types';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/Card';
import { apiClient } from '@/lib/api/client';
import toast from 'react-hot-toast';
import { ArrowLeft, Save, GraduationCap } from 'lucide-react';
import Link from 'next/link';

export default function CreateClassroomPage() {
  const router = useRouter();
  const { isTeacher } = useAuth();
  
  const [formData, setFormData] = useState<CreateClassroomRequest>({
    name: '',
    description: '',
    course_id: 0,
    grade: 1,
    start_date: '',
    end_date: '',
    fee_per_session: 0,
    max_students: 20,
  });
  
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(false);
  const [coursesLoading, setCoursesLoading] = useState(true);
  const [errors, setErrors] = useState<Partial<CreateClassroomRequest>>({});

  // Redirect if not teacher
  useEffect(() => {
    if (!isTeacher) {
      router.push('/dashboard');
    }
  }, [isTeacher, router]);

  // Fetch active courses
  useEffect(() => {
    if (isTeacher) {
      fetchActiveCourses();
    }
  }, [isTeacher]);

  const fetchActiveCourses = async () => {
    try {
      setCoursesLoading(true);
      const data = await apiClient.getActiveCourses();
      setCourses(data);
    } catch (error) {
      console.error('Error fetching courses:', error);
      toast.error('Không thể tải danh sách khóa học');
    } finally {
      setCoursesLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    let finalValue: any = value;
    
    if (type === 'number') {
      finalValue = parseFloat(value) || 0;
    }
    
    setFormData(prev => ({ ...prev, [name]: finalValue }));
    
    // Clear error when user starts typing
    if (errors[name as keyof CreateClassroomRequest]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<CreateClassroomRequest> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Tên lớp học là bắt buộc';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Tên lớp học phải có ít nhất 2 ký tự';
    }

    if (!formData.course_id || formData.course_id === 0) {
      newErrors.course_id = 'Vui lòng chọn khóa học';
    }

    if (!formData.grade || formData.grade < 1 || formData.grade > 12) {
      newErrors.grade = 'Lớp học phải từ 1 đến 12';
    }

    if (!formData.start_date) {
      newErrors.start_date = 'Ngày bắt đầu là bắt buộc';
    }

    if (!formData.end_date) {
      newErrors.end_date = 'Ngày kết thúc là bắt buộc';
    } else if (formData.start_date && new Date(formData.end_date) <= new Date(formData.start_date)) {
      newErrors.end_date = 'Ngày kết thúc phải sau ngày bắt đầu';
    }

    if (!formData.fee_per_session || formData.fee_per_session <= 0) {
      newErrors.fee_per_session = 'Học phí phải lớn hơn 0';
    }

    if (!formData.max_students || formData.max_students <= 0) {
      newErrors.max_students = 'Sĩ số tối đa phải lớn hơn 0';
    } else if (formData.max_students > 100) {
      newErrors.max_students = 'Sĩ số tối đa không được vượt quá 100';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setLoading(true);
      await apiClient.createClassroom(formData);
      toast.success('Tạo lớp học thành công!');
      router.push('/teacher/classes');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Tạo lớp học thất bại';
      toast.error(message);
    } finally {
      setLoading(false);
    }
  };

  if (!isTeacher) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/teacher/classes">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Tạo lớp học mới</h1>
            <p className="text-gray-600">Thêm lớp học mới vào khóa học</p>
          </div>
        </div>

        {/* Form */}
        <Card>
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <div className="flex items-center">
                <GraduationCap className="mr-2 h-5 w-5" />
                <CardTitle>Thông tin lớp học</CardTitle>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <Input
                label="Tên lớp học"
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                error={errors.name}
                placeholder="VD: Lớp A1 - Sáng"
                required
              />

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Khóa học <span className="text-red-500 ml-1">*</span>
                </label>
                {coursesLoading ? (
                  <div className="flex items-center justify-center py-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="ml-2 text-sm text-gray-500">Đang tải khóa học...</span>
                  </div>
                ) : (
                  <select
                    name="course_id"
                    value={formData.course_id}
                    onChange={handleChange}
                    className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value={0}>Chọn khóa học</option>
                    {courses.map((course) => (
                      <option key={course.id} value={course.id}>
                        {course.name}
                      </option>
                    ))}
                  </select>
                )}
                {errors.course_id && (
                  <p className="text-sm text-red-600">{errors.course_id}</p>
                )}
                {!coursesLoading && courses.length === 0 && (
                  <p className="text-sm text-yellow-600">
                    Chưa có khóa học nào. Vui lòng tạo khóa học trước.
                  </p>
                )}
              </div>

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Lớp học <span className="text-red-500 ml-1">*</span>
                </label>
                <select
                  name="grade"
                  value={formData.grade}
                  onChange={handleChange}
                  className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  {Array.from({ length: 12 }, (_, i) => i + 1).map((grade) => (
                    <option key={grade} value={grade}>
                      Lớp {grade}
                    </option>
                  ))}
                </select>
                {errors.grade && (
                  <p className="text-sm text-red-600">{errors.grade}</p>
                )}
                <p className="text-sm text-gray-500">
                  Chọn lớp học mà classroom này dành cho
                </p>
              </div>

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Mô tả lớp học
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                  className="flex w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Mô tả về lớp học này..."
                />
                {errors.description && (
                  <p className="text-sm text-red-600">{errors.description}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Ngày bắt đầu"
                  type="date"
                  name="start_date"
                  value={formData.start_date}
                  onChange={handleChange}
                  error={errors.start_date}
                  required
                />

                <Input
                  label="Ngày kết thúc"
                  type="date"
                  name="end_date"
                  value={formData.end_date}
                  onChange={handleChange}
                  error={errors.end_date}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Học phí mỗi buổi (VNĐ)"
                  type="number"
                  name="fee_per_session"
                  value={formData.fee_per_session}
                  onChange={handleChange}
                  error={errors.fee_per_session}
                  placeholder="100000"
                  min="0"
                  step="1000"
                  required
                />

                <Input
                  label="Sĩ số tối đa"
                  type="number"
                  name="max_students"
                  value={formData.max_students}
                  onChange={handleChange}
                  error={errors.max_students}
                  placeholder="20"
                  min="1"
                  max="100"
                  required
                />
              </div>
            </CardContent>

            <CardFooter className="flex justify-end space-x-4">
              <Link href="/teacher/classes">
                <Button variant="outline" disabled={loading}>
                  Hủy
                </Button>
              </Link>
              <Button
                type="submit"
                loading={loading}
                disabled={loading || courses.length === 0}
              >
                <Save className="mr-2 h-4 w-4" />
                {loading ? 'Đang tạo...' : 'Tạo lớp học'}
              </Button>
            </CardFooter>
          </form>
        </Card>

        {/* Info Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Lưu ý</CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-gray-600 space-y-2">
            <p>• Lớp học sẽ được kích hoạt ngay sau khi tạo</p>
            <p>• Học sinh có thể đăng ký vào lớp học sau khi được tạo</p>
            <p>• Thời gian lớp học nên nằm trong khoảng thời gian của khóa học</p>
            <p>• Có thể chỉnh sửa thông tin lớp học sau khi tạo</p>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
