# 🚀 Frontend Production Setup với PM2

## 📋 **<PERSON><PERSON><PERSON> c<PERSON>u hệ thống**

- Node.js >= 18
- PM2 (Process Manager)
- Backend đã chạy trên port 9005

## 🔧 **Cài đặt PM2 (nếu chưa c<PERSON>)**

```bash
# Cài đặt PM2 globally
npm install -g pm2

# Kiểm tra PM2 đã cài đặt
pm2 --version
```

## 🚀 **Các bước chạy Production**

### **BƯỚC 1: Chuẩn bị môi trường**

```bash
# 1. Di chuyển vào thư mục frontend
cd front-end

# 2. Cài đặt dependencies
npm install

# 3. Tạo file .env từ template
cp .env.production .env.local
```

### **BƯỚC 2: Cấu hình Environment**

Kiểm tra file `.env.local`:
```bash
NODE_ENV=production
PORT=9006
NEXT_PUBLIC_API_URL=http://localhost:9005/api
```

**⚠️ <PERSON>uan trọng**: <PERSON><PERSON><PERSON> bảo `NEXT_PUBLIC_API_URL` trỏ đúng đến backend (port 9005)

### **BƯỚC 3: Build ứng dụng**

```bash
# Build Next.js cho production
npm run build
```

### **BƯỚC 4: Chạy với PM2**

```bash
# Khởi động với PM2
npm run pm2:start

# Hoặc chạy trực tiếp
pm2 start ecosystem.config.js --env production
```

### **BƯỚC 5: Kiểm tra hoạt động**

```bash
# Kiểm tra status
npm run pm2:status

# Xem logs
npm run pm2:logs

# Kiểm tra website
curl http://localhost:9006
```

## 📊 **Quản lý PM2**

### **Các lệnh cơ bản:**

```bash
# Xem status tất cả processes
pm2 status

# Xem logs realtime
pm2 logs cms-frontend

# Restart ứng dụng
npm run pm2:restart

# Stop ứng dụng
npm run pm2:stop

# Xóa process khỏi PM2
npm run pm2:delete
```

### **Monitoring:**

```bash
# Xem dashboard
pm2 monit

# Xem logs với filter
pm2 logs cms-frontend --lines 100

# Xem memory usage
pm2 show cms-frontend
```

## 🔧 **Cấu hình nâng cao**

### **Tự động khởi động khi reboot:**

```bash
# Lưu cấu hình PM2 hiện tại
pm2 save

# Tạo startup script
pm2 startup

# Chạy lệnh được suggest (thường cần sudo)
```

### **Load balancing (nếu cần):**

Sửa file `ecosystem.config.js`:
```javascript
instances: 'max', // Sử dụng tất cả CPU cores
exec_mode: 'cluster'
```

## 🌐 **Kiểm tra kết nối**

### **1. Health Check:**
```bash
curl http://localhost:9006
```

### **2. API Connection Test:**
```bash
# Test từ frontend đến backend
curl http://localhost:9006/api/health
```

### **3. Browser Test:**
- Mở: http://localhost:9006
- Login với admin/admin123
- Kiểm tra các tính năng

## 🐛 **Troubleshooting**

### **Port đã được sử dụng:**
```bash
# Kiểm tra process đang dùng port 9006
netstat -tulpn | grep :9006

# Kill process nếu cần
kill -9 <PID>
```

### **Build failed:**
```bash
# Xóa cache và rebuild
rm -rf .next
npm run build
```

### **API connection failed:**
```bash
# Kiểm tra backend có chạy không
curl http://localhost:9005/health

# Kiểm tra CORS settings
curl -H "Origin: http://localhost:9006" http://localhost:9005/api
```

### **PM2 issues:**
```bash
# Restart PM2 daemon
pm2 kill
pm2 resurrect

# Xóa tất cả processes
pm2 delete all
```

## 📁 **Cấu trúc Files**

```
front-end/
├── ecosystem.config.js     # PM2 configuration
├── .env.production        # Production environment template
├── .env.local            # Local environment (copy from .env.production)
├── next.config.ts        # Next.js configuration
├── logs/                 # PM2 logs directory
│   ├── err.log
│   ├── out.log
│   └── combined.log
└── PRODUCTION_SETUP.md   # This file
```

## ✅ **Checklist Production Ready**

- [ ] ✅ Backend chạy trên port 9005
- [ ] ✅ PM2 đã cài đặt
- [ ] ✅ Dependencies đã install
- [ ] ✅ Build thành công
- [ ] ✅ PM2 process running
- [ ] ✅ Frontend accessible trên port 9006
- [ ] ✅ API connection hoạt động
- [ ] ✅ Login/Authentication works
- [ ] ✅ All features functional

## 🎯 **Production URLs**

- **Frontend**: http://localhost:9006
- **Backend API**: http://localhost:9005/api
- **Backend Health**: http://localhost:9005/health
- **Database**: localhost:9007 (PostgreSQL)

## 📈 **Performance Tips**

1. **Enable gzip compression** trong reverse proxy
2. **Use CDN** cho static assets
3. **Monitor memory usage** với PM2
4. **Set up log rotation** để tránh logs quá lớn
5. **Use HTTPS** trong production thực tế

## 🔒 **Security Notes**

- Thay đổi default ports nếu cần
- Sử dụng environment variables cho sensitive data
- Enable HTTPS trong production
- Cấu hình firewall cho ports cần thiết
- Regular backup và monitoring
