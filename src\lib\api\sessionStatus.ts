import { apiClient } from './client';

export interface SessionStatusUpdateResult {
  success: boolean;
  message: string;
  totalChecked: number;
  totalUpdated: number;
  details: {
    toInProgress: number;
    toCompleted: number;
    errors: Array<{
      sessionId: number;
      error: string;
    }>;
  };
  timestamp: string;
}

export interface SessionStatusStats {
  date: string;
  totalSessions: number;
  statusBreakdown: {
    scheduled?: number;
    in_progress?: number;
    completed?: number;
    cancelled?: number;
    postponed?: number;
  };
  timestamp: string;
}

export interface SchedulerStatus {
  scheduler: {
    initialized: boolean;
    jobs: {
      [jobName: string]: {
        running: boolean;
        scheduled: boolean;
      };
    };
  };
  timestamp: string;
}

export interface CalculateStatusRequest {
  sessionDate: string; // YYYY-MM-DD
  startTime: string;   // HH:mm:ss
  endTime: string;     // HH:mm:ss
}

export interface CalculateStatusResponse {
  input: {
    sessionDate: string;
    startTime: string;
    endTime: string;
  };
  current: {
    date: string;
    time: string;
  };
  calculatedStatus: string;
  timestamp: string;
}

export const sessionStatusApi = {
  // Update all session statuses (Admin only)
  updateAllSessionStatuses: async (): Promise<SessionStatusUpdateResult> => {
    const response = await apiClient.post('/session-status/update-all');
    return response.data.data;
  },

  // Update session statuses for a specific classroom (Teacher)
  updateClassroomSessionStatuses: async (classroomId: number): Promise<SessionStatusUpdateResult> => {
    const response = await apiClient.post(`/session-status/update-classroom/${classroomId}`);
    return response.data.data;
  },

  // Get session status update statistics
  getUpdateStats: async (): Promise<SessionStatusStats> => {
    const response = await apiClient.get('/session-status/stats');
    return response.data.data;
  },

  // Get scheduler status (Admin only)
  getSchedulerStatus: async (): Promise<SchedulerStatus> => {
    const response = await apiClient.get('/session-status/scheduler');
    return response.data.data;
  },

  // Run manual session status update (Admin only)
  runManualUpdate: async (): Promise<SessionStatusUpdateResult> => {
    const response = await apiClient.post('/session-status/manual-update');
    return response.data.data;
  },

  // Control scheduler jobs (Admin only)
  controlScheduler: async (action: 'start' | 'stop' | 'restart', jobName?: string): Promise<any> => {
    const response = await apiClient.post('/session-status/scheduler/control', {
      action,
      jobName
    });
    return response.data.data;
  },

  // Calculate session status for given date/time (utility)
  calculateSessionStatus: async (data: CalculateStatusRequest): Promise<CalculateStatusResponse> => {
    const response = await apiClient.post('/session-status/calculate', data);
    return response.data.data;
  }
};
