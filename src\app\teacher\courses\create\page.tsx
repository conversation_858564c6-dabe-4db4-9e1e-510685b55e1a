'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { CreateCourseRequest } from '@/types';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/Card';
import { apiClient } from '@/lib/api/client';
import toast from 'react-hot-toast';
import { ArrowLeft, Save, BookOpen } from 'lucide-react';
import Link from 'next/link';

export default function CreateCoursePage() {
  const router = useRouter();
  const { isTeacher } = useAuth();
  
  const [formData, setFormData] = useState<CreateCourseRequest>({
    name: '',
    description: '',
    start_date: '',
    end_date: '',
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<CreateCourseRequest>>({});

  // Redirect if not teacher
  React.useEffect(() => {
    if (!isTeacher) {
      router.push('/dashboard');
    }
  }, [isTeacher, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name as keyof CreateCourseRequest]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<CreateCourseRequest> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Tên khóa học là bắt buộc';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Tên khóa học phải có ít nhất 2 ký tự';
    }

    if (!formData.start_date) {
      newErrors.start_date = 'Ngày bắt đầu là bắt buộc';
    }

    if (!formData.end_date) {
      newErrors.end_date = 'Ngày kết thúc là bắt buộc';
    } else if (formData.start_date && new Date(formData.end_date) <= new Date(formData.start_date)) {
      newErrors.end_date = 'Ngày kết thúc phải sau ngày bắt đầu';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setLoading(true);
      await apiClient.createCourse(formData);
      toast.success('Tạo khóa học thành công!');
      router.push('/teacher/courses');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Tạo khóa học thất bại';
      toast.error(message);
    } finally {
      setLoading(false);
    }
  };

  if (!isTeacher) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/teacher/courses">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Tạo khóa học mới</h1>
            <p className="text-gray-600">Thêm khóa học mới vào hệ thống</p>
          </div>
        </div>

        {/* Form */}
        <Card>
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <div className="flex items-center">
                <BookOpen className="mr-2 h-5 w-5" />
                <CardTitle>Thông tin khóa học</CardTitle>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <Input
                label="Tên khóa học"
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                error={errors.name}
                placeholder="VD: Khóa học 2024-2025"
                required
              />

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Mô tả khóa học
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                  className="flex w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Mô tả về khóa học này..."
                />
                {errors.description && (
                  <p className="text-sm text-red-600">{errors.description}</p>
                )}
                <p className="text-sm text-gray-500">
                  Mô tả ngắn gọn về nội dung và mục tiêu của khóa học
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Ngày bắt đầu"
                  type="date"
                  name="start_date"
                  value={formData.start_date}
                  onChange={handleChange}
                  error={errors.start_date}
                  required
                />

                <Input
                  label="Ngày kết thúc"
                  type="date"
                  name="end_date"
                  value={formData.end_date}
                  onChange={handleChange}
                  error={errors.end_date}
                  required
                />
              </div>
            </CardContent>

            <CardFooter className="flex justify-end space-x-4">
              <Link href="/teacher/courses">
                <Button variant="outline" disabled={loading}>
                  Hủy
                </Button>
              </Link>
              <Button
                type="submit"
                loading={loading}
                disabled={loading}
              >
                <Save className="mr-2 h-4 w-4" />
                {loading ? 'Đang tạo...' : 'Tạo khóa học'}
              </Button>
            </CardFooter>
          </form>
        </Card>

        {/* Info Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Lưu ý</CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-gray-600 space-y-2">
            <p>• Khóa học sẽ được kích hoạt ngay sau khi tạo</p>
            <p>• Bạn có thể tạo nhiều lớp học trong một khóa học</p>
            <p>• Thời gian khóa học nên được đặt phù hợp với năm học</p>
            <p>• Có thể chỉnh sửa thông tin khóa học sau khi tạo</p>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
