'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Course } from '@/types';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/Card';
import { coursesApi } from '@/lib/api/courses';
import toast from 'react-hot-toast';
import { ArrowLeft, Save, BookOpen } from 'lucide-react';
import Link from 'next/link';

export default function EditCoursePage() {
  const router = useRouter();
  const params = useParams();
  const { isTeacher } = useAuth();
  const courseId = parseInt(params.id as string);
  
  const [course, setCourse] = useState<Course | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    start_date: '',
    end_date: '',
    is_active: true,
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Partial<typeof formData>>({});

  // Redirect if not teacher
  useEffect(() => {
    if (!isTeacher) {
      router.push('/dashboard');
    }
  }, [isTeacher, router]);

  // Fetch course data
  useEffect(() => {
    if (isTeacher && courseId) {
      fetchCourse();
    }
  }, [isTeacher, courseId]);

  const fetchCourse = async () => {
    try {
      setLoading(true);
      const data = await apiClient.getCourseById(courseId);
      setCourse(data);
      setFormData({
        name: data.name,
        description: data.description || '',
        start_date: data.start_date.split('T')[0], // Convert to YYYY-MM-DD format
        end_date: data.end_date.split('T')[0],
        is_active: data.is_active,
      });
    } catch (error) {
      console.error('Error fetching course:', error);
      toast.error('Không thể tải thông tin khóa học');
      router.push('/teacher/courses');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const finalValue = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;
    
    setFormData(prev => ({ ...prev, [name]: finalValue }));
    
    // Clear error when user starts typing
    if (errors[name as keyof typeof formData]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<typeof formData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Tên khóa học là bắt buộc';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Tên khóa học phải có ít nhất 2 ký tự';
    }

    if (!formData.start_date) {
      newErrors.start_date = 'Ngày bắt đầu là bắt buộc';
    }

    if (!formData.end_date) {
      newErrors.end_date = 'Ngày kết thúc là bắt buộc';
    } else if (formData.start_date && new Date(formData.end_date) <= new Date(formData.start_date)) {
      newErrors.end_date = 'Ngày kết thúc phải sau ngày bắt đầu';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setSaving(true);
      await apiClient.updateCourse(courseId, formData);
      toast.success('Cập nhật khóa học thành công!');
      router.push('/teacher/courses');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Cập nhật thất bại';
      toast.error(message);
    } finally {
      setSaving(false);
    }
  };

  if (!isTeacher) {
    return null;
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Đang tải thông tin khóa học...</span>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/teacher/courses">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Chỉnh sửa khóa học</h1>
            <p className="text-gray-600">Cập nhật thông tin khóa học: {course?.name}</p>
          </div>
        </div>

        {/* Form */}
        <Card>
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <div className="flex items-center">
                <BookOpen className="mr-2 h-5 w-5" />
                <CardTitle>Thông tin khóa học</CardTitle>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <Input
                label="Tên khóa học"
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                error={errors.name}
                placeholder="VD: Khóa học 2024-2025"
                required
              />

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Mô tả khóa học
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                  className="flex w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Mô tả về khóa học này..."
                />
                {errors.description && (
                  <p className="text-sm text-red-600">{errors.description}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Ngày bắt đầu"
                  type="date"
                  name="start_date"
                  value={formData.start_date}
                  onChange={handleChange}
                  error={errors.start_date}
                  required
                />

                <Input
                  label="Ngày kết thúc"
                  type="date"
                  name="end_date"
                  value={formData.end_date}
                  onChange={handleChange}
                  error={errors.end_date}
                  required
                />
              </div>

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Trạng thái khóa học
                </label>
                <select
                  name="is_active"
                  value={formData.is_active.toString()}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.value === 'true' }))}
                  className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="true">Đang hoạt động</option>
                  <option value="false">Không hoạt động</option>
                </select>
                <p className="text-sm text-gray-500">
                  Khóa học không hoạt động sẽ không thể tạo lớp học mới
                </p>
              </div>

              {course && (
                <div className="bg-gray-50 p-4 rounded-md space-y-2">
                  <h4 className="font-medium text-gray-900">Thông tin bổ sung</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">ID:</span>
                      <span className="ml-2 font-medium">{course.id}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Ngày tạo:</span>
                      <span className="ml-2 font-medium">
                        {new Date(course.created_at).toLocaleDateString('vi-VN')}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">Lần cập nhật cuối:</span>
                      <span className="ml-2 font-medium">
                        {new Date(course.updated_at).toLocaleDateString('vi-VN')}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>

            <CardFooter className="flex justify-end space-x-4">
              <Link href="/teacher/courses">
                <Button variant="outline" disabled={saving}>
                  Hủy
                </Button>
              </Link>
              <Button
                type="submit"
                loading={saving}
                disabled={saving}
              >
                <Save className="mr-2 h-4 w-4" />
                {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
              </Button>
            </CardFooter>
          </form>
        </Card>

        {/* Warning Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm text-yellow-700">Lưu ý quan trọng</CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-gray-600 space-y-2">
            <p>• Thay đổi thời gian khóa học có thể ảnh hưởng đến các lớp học đã tạo</p>
            <p>• Vô hiệu hóa khóa học sẽ ngăn việc tạo lớp học mới</p>
            <p>• Các lớp học hiện tại vẫn hoạt động bình thường</p>
            <p>• Mọi thay đổi sẽ có hiệu lực ngay lập tức</p>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
