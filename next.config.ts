import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api"
  },
  // Enable standalone output for production
  output: 'standalone',
  // Optimize for production
  experimental: {
    optimizeCss: true,
  },
  // Image optimization
  images: {
    domains: ['res.cloudinary.com', 'localhost'],
  },
};

export default nextConfig;
