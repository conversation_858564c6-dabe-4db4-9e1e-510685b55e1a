import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || "https://vuquangduy.online/api"
  },
  // Enable standalone output for production
  output: 'standalone',
  // Optimize for production
  experimental: {
    // optimizeCss: true, // Disabled due to critters module issue
  },
  // Image optimization
  images: {
    domains: ['res.cloudinary.com', 'localhost'],
  },
  // Disable linting during build
  eslint: { 
    ignoreDuringBuilds: true,
  },
};

export default nextConfig;
