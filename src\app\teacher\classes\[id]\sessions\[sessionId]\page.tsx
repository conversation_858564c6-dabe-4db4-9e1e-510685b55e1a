'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import { 
  ArrowLeft, 
  Calendar, 
  Clock, 
  MapPin,
  Users,
  UserCheck,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Play,
  Pause
} from 'lucide-react';

import Button from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/badge';
import AttendanceManager from '@/components/AttendanceManager';

import { sessionsApi } from '@/lib/api/sessions';
import { classroomsApi } from '@/lib/api/classrooms';
import { Session, Classroom } from '@/types';
import { formatDate, formatTime } from '@/lib/utils';

export default function SessionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const classroomId = parseInt(params.id as string);
  const sessionId = parseInt(params.sessionId as string);

  const [session, setSession] = useState<Session | null>(null);
  const [classroom, setClassroom] = useState<Classroom | null>(null);
  const [loading, setLoading] = useState(true);
  const [showAttendance, setShowAttendance] = useState(false);

  useEffect(() => {
    fetchData();
  }, [sessionId, classroomId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [sessionData, classroomData] = await Promise.all([
        sessionsApi.getSessionById(sessionId),
        classroomsApi.getClassroomById(classroomId)
      ]);

      setSession(sessionData);
      setClassroom(classroomData);
    } catch (error) {
      console.error('Failed to fetch session data:', error);
      toast.error('Không thể tải thông tin buổi học');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      scheduled: { label: 'Đã lên lịch', className: 'bg-blue-100 text-blue-800' },
      in_progress: { label: 'Đang diễn ra', className: 'bg-green-100 text-green-800' },
      completed: { label: 'Hoàn thành', className: 'bg-gray-100 text-gray-800' },
      cancelled: { label: 'Đã hủy', className: 'bg-red-100 text-red-800' },
      postponed: { label: 'Hoãn lại', className: 'bg-yellow-100 text-yellow-800' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.scheduled;
    
    return (
      <Badge className={config.className}>
        {config.label}
      </Badge>
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Clock className="w-5 h-5 text-blue-600" />;
      case 'in_progress':
        return <Play className="w-5 h-5 text-green-600" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-gray-600" />;
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-600" />;
      case 'postponed':
        return <Pause className="w-5 h-5 text-yellow-600" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session || !classroom) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Không tìm thấy buổi học</h2>
        <Link href={`/teacher/classes/${classroomId}/sessions`}>
          <Button>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Quay lại danh sách
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href={`/teacher/classes/${classroomId}/sessions`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Quay lại
            </Button>
          </Link>
          
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {session.title || 'Chi tiết buổi học'}
            </h1>
            <p className="text-gray-600">
              {classroom.name} - {classroom.course?.name}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {getStatusBadge(session.status)}
          
          {session.status === 'in_progress' && (
            <Button
              onClick={() => setShowAttendance(true)}
              className="bg-green-600 hover:bg-green-700"
            >
              <UserCheck className="w-4 h-4 mr-2" />
              Điểm danh
            </Button>
          )}
        </div>
      </div>

      {/* Session Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getStatusIcon(session.status)}
            Thông tin buổi học
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Ngày học</p>
                  <p className="font-medium">{formatDate(session.date)}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Thời gian</p>
                  <p className="font-medium">
                    {formatTime(session.start_time)} - {formatTime(session.end_time)}
                  </p>
                </div>
              </div>

              {session.location && (
                <div className="flex items-center gap-3">
                  <MapPin className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Địa điểm</p>
                    <p className="font-medium">{session.location}</p>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Users className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Lớp học</p>
                  <p className="font-medium">{classroom.name}</p>
                </div>
              </div>

              {session.description && (
                <div>
                  <p className="text-sm text-gray-600 mb-2">Mô tả</p>
                  <p className="text-gray-900">{session.description}</p>
                </div>
              )}

              {session.notes && (
                <div>
                  <p className="text-sm text-gray-600 mb-2">Ghi chú</p>
                  <p className="text-gray-900">{session.notes}</p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Thao tác</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            {session.status === 'in_progress' && (
              <Button
                onClick={() => setShowAttendance(true)}
                className="bg-green-600 hover:bg-green-700"
              >
                <UserCheck className="w-4 h-4 mr-2" />
                Điểm danh học sinh
              </Button>
            )}

            <Button variant="outline">
              <Edit className="w-4 h-4 mr-2" />
              Chỉnh sửa
            </Button>

            <Button variant="outline" className="text-red-600 hover:text-red-700">
              <Trash2 className="w-4 h-4 mr-2" />
              Xóa buổi học
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Attendance Manager Modal */}
      {showAttendance && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <AttendanceManager
              sessionId={sessionId}
              onClose={() => setShowAttendance(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
