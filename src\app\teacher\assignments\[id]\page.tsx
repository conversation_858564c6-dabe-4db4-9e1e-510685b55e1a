'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { AssignmentDetail } from '@/components/assignments';

export default function TeacherAssignmentDetailPage() {
  const params = useParams();
  const assignmentId = parseInt(params.id as string);
  const { user } = useAuth();

  // Redirect if not teacher
  if (user?.role !== 'teacher') {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600">Access denied. Only teachers can view this page.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-gray-600">
        <a href="/teacher/classes" className="hover:text-blue-600">
          Classes
        </a>
        <span>›</span>
        <span className="text-gray-900">Assignment Details</span>
      </nav>

      {/* Assignment Detail */}
      <AssignmentDetail assignmentId={assignmentId} />
    </div>
  );
}
