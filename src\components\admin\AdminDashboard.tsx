'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { TeacherWithStats, PaginatedResponse } from '@/types';
import { formatDateTime, getRoleDisplayName, getStatusColor } from '@/lib/utils';
import { teachersApi } from '@/lib/api/teachers';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { Users, UserCheck, UserX, Plus } from 'lucide-react';
import toast from 'react-hot-toast';

export default function AdminDashboard() {
  const { user } = useAuth();
  const [teachers, setTeachers] = useState<PaginatedResponse<TeacherWithStats> | null>(null);
  const [loading, setLoading] = useState(true);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    fetchTeachers();
  }, []);

  const fetchTeachers = async () => {
    try {
      setLoading(true);
      const data = await teachersApi.getTeachers({ page: 0, limit: 5 });
      setTeachers(data);
    } catch (error) {
      console.error('Error fetching teachers:', error);
      toast.error('Không thể tải danh sách giáo viên');
    } finally {
      setLoading(false);
      setInitialLoading(false);
    }
  };

  const toggleTeacherStatus = async (teacherId: number, currentStatus: boolean) => {
    try {
      await teachersApi.toggleTeacherStatus(teacherId, !currentStatus);
      toast.success(`${!currentStatus ? 'Kích hoạt' : 'Vô hiệu hóa'} giáo viên thành công`);
      fetchTeachers(); // Refresh list
    } catch (error) {
      console.error('Error toggling teacher status:', error);
      toast.error('Không thể cập nhật trạng thái giáo viên');
    }
  };

  const stats = teachers && teachers.items ? {
    total: teachers.total,
    active: teachers.items.filter(t => t.is_active).length,
    inactive: teachers.items.filter(t => !t.is_active).length,
  } : { total: 0, active: 0, inactive: 0 };

  // Show loading spinner on initial load
  if (initialLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Đang tải dashboard...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Chào mừng, {user?.name}!
        </h1>
        <p className="text-gray-600">
          Quản lý hệ thống và tài khoản giáo viên
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Tổng giáo viên</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserCheck className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Đang hoạt động</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserX className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Vô hiệu hóa</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.inactive}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Teachers Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Quản lý giáo viên</CardTitle>
            <Link href="/admin/teachers/create">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Thêm giáo viên
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-500">Đang tải...</p>
            </div>
          ) : teachers && teachers.items && teachers.items.length > 0 ? (
            <div className="space-y-4">
              {teachers.items.map((teacher) => (
                <div key={teacher.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <span className="text-sm font-medium text-gray-700">
                            {teacher.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {teacher.name}
                        </p>
                        <p className="text-sm text-gray-500 truncate">
                          {teacher.email}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="text-sm text-gray-500">Tham gia</p>
                      <p className="text-sm font-medium">
                        {formatDateTime(teacher.created_at)}
                      </p>
                    </div>
                    
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(teacher.is_active ? 'active' : 'inactive')}`}>
                      {teacher.is_active ? 'Hoạt động' : 'Vô hiệu hóa'}
                    </span>
                    
                    <Button
                      variant={teacher.is_active ? 'danger' : 'primary'}
                      size="sm"
                      onClick={() => toggleTeacherStatus(teacher.id, teacher.is_active)}
                    >
                      {teacher.is_active ? 'Vô hiệu hóa' : 'Kích hoạt'}
                    </Button>
                  </div>
                </div>
              ))}
              
              {teachers && teachers.total > 5 && (
                <div className="text-center pt-4">
                  <Link href="/admin/teachers">
                    <Button variant="outline">
                      Xem tất cả ({teachers.total} giáo viên)
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Chưa có giáo viên</h3>
              <p className="mt-1 text-sm text-gray-500">
                Bắt đầu bằng cách tạo tài khoản giáo viên đầu tiên.
              </p>
              <div className="mt-6">
                <Link href="/admin/teachers/create">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Thêm giáo viên
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
