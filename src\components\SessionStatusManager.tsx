'use client';

import React, { useState, useEffect } from 'react';
import { sessionStatusApi, SessionStatusStats, SessionStatusUpdateResult } from '@/lib/api/sessionStatus';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'react-hot-toast';
import { RefreshCw, Clock, CheckCircle, XCircle, Pause, Play } from 'lucide-react';

interface SessionStatusManagerProps {
  classroomId?: number; // If provided, only manage this classroom
  showAdminControls?: boolean; // Show admin-only controls
}

export default function SessionStatusManager({ 
  classroomId, 
  showAdminControls = false 
}: SessionStatusManagerProps) {
  const { user, isAdmin } = useAuth();
  const [stats, setStats] = useState<SessionStatusStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);

  // Load stats on component mount
  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      const statsData = await sessionStatusApi.getUpdateStats();
      setStats(statsData);
    } catch (error: any) {
      console.error('Failed to load session stats:', error);
      toast.error('Không thể tải thống kê sessions');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSessions = async () => {
    try {
      setUpdating(true);
      let result: SessionStatusUpdateResult;

      if (classroomId) {
        // Update specific classroom
        result = await sessionStatusApi.updateClassroomSessionStatuses(classroomId);
        toast.success(`Đã cập nhật ${result.totalUpdated} sessions cho lớp học`);
      } else if (isAdmin) {
        // Update all sessions (admin only)
        result = await sessionStatusApi.runManualUpdate();
        toast.success(`Đã cập nhật ${result.totalUpdated} sessions tổng cộng`);
      } else {
        toast.error('Không có quyền thực hiện thao tác này');
        return;
      }

      // Show detailed results if there were updates
      if (result.totalUpdated > 0) {
        const details = result.details;
        if (details.toInProgress > 0) {
          toast.success(`${details.toInProgress} sessions đã chuyển sang "Đang diễn ra"`);
        }
        if (details.toCompleted > 0) {
          toast.success(`${details.toCompleted} sessions đã chuyển sang "Hoàn thành"`);
        }
      }

      setLastUpdate(new Date().toLocaleString('vi-VN'));
      
      // Reload stats after update
      await loadStats();

    } catch (error: any) {
      console.error('Failed to update sessions:', error);
      toast.error('Không thể cập nhật trạng thái sessions');
    } finally {
      setUpdating(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'in_progress':
        return <Play className="w-4 h-4 text-green-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-gray-500" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'postponed':
        return <Pause className="w-4 h-4 text-yellow-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'Đã lên lịch';
      case 'in_progress':
        return 'Đang diễn ra';
      case 'completed':
        return 'Hoàn thành';
      case 'cancelled':
        return 'Đã hủy';
      case 'postponed':
        return 'Hoãn lại';
      default:
        return status;
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Trạng thái Sessions
          </h3>
          <p className="text-sm text-gray-600">
            {classroomId ? 'Lớp học này' : 'Tất cả sessions hôm nay'}
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <button
            onClick={loadStats}
            disabled={loading}
            className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            Làm mới
          </button>
          
          <button
            onClick={handleUpdateSessions}
            disabled={updating || (!classroomId && !isAdmin)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RefreshCw className={`w-4 h-4 ${updating ? 'animate-spin' : ''}`} />
            {updating ? 'Đang cập nhật...' : 'Cập nhật trạng thái'}
          </button>
        </div>
      </div>

      {/* Stats Display */}
      {stats && (
        <div className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {Object.entries(stats.statusBreakdown).map(([status, count]) => (
              <div key={status} className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  {getStatusIcon(status)}
                  <span className="text-sm font-medium text-gray-700">
                    {getStatusLabel(status)}
                  </span>
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  {count || 0}
                </div>
              </div>
            ))}
          </div>

          <div className="flex items-center justify-between text-sm text-gray-600 pt-4 border-t">
            <span>
              Tổng cộng: <strong>{stats.totalSessions}</strong> sessions hôm nay
            </span>
            {lastUpdate && (
              <span>
                Cập nhật lần cuối: <strong>{lastUpdate}</strong>
              </span>
            )}
          </div>
        </div>
      )}

      {/* Auto-update Info */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start gap-3">
          <Clock className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-blue-900 mb-1">
              Cập nhật tự động
            </h4>
            <p className="text-sm text-blue-700">
              Hệ thống tự động cập nhật trạng thái sessions mỗi 5 phút dựa trên thời gian thực tế. 
              Sessions sẽ chuyển sang "Đang diễn ra" khi bắt đầu và "Hoàn thành" khi kết thúc.
            </p>
          </div>
        </div>
      </div>

      {/* Admin Controls */}
      {showAdminControls && isAdmin && (
        <div className="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <h4 className="text-sm font-medium text-yellow-900 mb-2">
            Điều khiển Admin
          </h4>
          <p className="text-sm text-yellow-700 mb-3">
            Các chức năng quản trị để kiểm soát hệ thống cập nhật tự động.
          </p>
          <div className="flex gap-2">
            <button
              onClick={() => toast('Tính năng này sẽ được triển khai trong phiên bản tiếp theo')}
              className="px-3 py-1 text-xs bg-yellow-600 text-white rounded hover:bg-yellow-700"
            >
              Xem Scheduler
            </button>
            <button
              onClick={() => toast('Tính năng này sẽ được triển khai trong phiên bản tiếp theo')}
              className="px-3 py-1 text-xs bg-yellow-600 text-white rounded hover:bg-yellow-700"
            >
              Cấu hình
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
