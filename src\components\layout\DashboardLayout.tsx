'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { getRoleDisplayName } from '@/lib/utils';
import Button from '@/components/ui/Button';
import { 
  Users, 
  BookOpen, 
  GraduationCap, 
  Calendar, 
  CheckSquare, 
  DollarSign, 
  FileText, 
  Settings, 
  LogOut,
  Menu,
  X,
  Home
} from 'lucide-react';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, logout, isAdmin, isTeacher, isStudent } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleLogout = async () => {
    await logout();
    router.push('/login');
  };

  // Navigation items based on role
  const getNavigationItems = () => {
    const items = [
      { name: 'Dashboard', href: '/dashboard', icon: Home, roles: ['admin', 'teacher', 'student'] },
    ];

    if (isAdmin) {
      items.push(
        { name: 'Quản lý giáo viên', href: '/admin/teachers', icon: Users, roles: ['admin'] },
      );
    }

    if (isTeacher) {
      items.push(
        { name: 'Khóa học', href: '/teacher/courses', icon: BookOpen, roles: ['teacher'] },
        { name: 'Lớp học', href: '/teacher/classes', icon: GraduationCap, roles: ['teacher'] },
        { name: 'Bài tập', href: '/teacher/assignments', icon: FileText, roles: ['teacher'] },
        { name: 'Đăng ký học sinh', href: '/teacher/enrollments', icon: Users, roles: ['teacher'] },
        { name: 'Lịch học', href: '/teacher/classes', icon: Calendar, roles: ['teacher'] },
        { name: 'Điểm danh', href: '/teacher/attendance', icon: CheckSquare, roles: ['teacher'] },
        { name: 'Học phí', href: '/teacher/tuition', icon: DollarSign, roles: ['teacher'] },
        { name: 'Báo cáo', href: '/teacher/reports', icon: FileText, roles: ['teacher'] },
      );
    }

    if (isStudent) {
      items.push(
        { name: 'Lớp của tôi', href: '/student/my-classes', icon: BookOpen, roles: ['student'] },
        { name: 'Đăng ký lớp', href: '/student/classes', icon: GraduationCap, roles: ['student'] },
        { name: 'Lịch học', href: '/student/schedule', icon: Calendar, roles: ['student'] },
        { name: 'Điểm danh', href: '/student/attendance', icon: CheckSquare, roles: ['student'] },
        { name: 'Học phí', href: '/student/fees', icon: DollarSign, roles: ['student'] },
      );
    }

    items.push(
      { name: 'Cài đặt', href: '/settings', icon: Settings, roles: ['admin', 'teacher', 'student'] },
    );

    return items.filter(item => item.roles.includes(user?.role || ''));
  };

  const navigationItems = getNavigationItems();

  const isActiveLink = (href: string) => {
    return pathname === href || pathname.startsWith(href + '/');
  };

  const Sidebar = ({ mobile = false }) => (
    <div className={`${mobile ? 'lg:hidden' : 'hidden lg:flex'} flex-col w-64 bg-gray-900`}>
      <div className="flex items-center justify-between h-16 px-4 bg-gray-800">
        <div className="flex items-center">
          <GraduationCap className="h-8 w-8 text-white" />
          <span className="ml-2 text-white font-semibold">SMS</span>
        </div>
        {mobile && (
          <button
            onClick={() => setSidebarOpen(false)}
            className="text-gray-400 hover:text-white"
          >
            <X className="h-6 w-6" />
          </button>
        )}
      </div>

      <nav className="flex-1 px-4 py-4 space-y-2">
        {navigationItems.map((item) => {
          const Icon = item.icon;
          const active = isActiveLink(item.href);
          
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                active
                  ? 'bg-gray-800 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              }`}
              onClick={() => mobile && setSidebarOpen(false)}
            >
              <Icon className="mr-3 h-5 w-5" />
              {item.name}
            </Link>
          );
        })}
      </nav>

      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center mb-3">
          <div className="flex-shrink-0">
            <div className="h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center">
              <span className="text-sm font-medium text-white">
                {user?.name ? user.name.charAt(0).toUpperCase() : 'U'}
              </span>
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-white">{user?.name}</p>
            <p className="text-xs text-gray-400">{getRoleDisplayName(user?.role || '')}</p>
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleLogout}
          className="w-full text-gray-300 border-gray-600 hover:bg-gray-700"
        >
          <LogOut className="mr-2 h-4 w-4" />
          Đăng xuất
        </Button>
      </div>
    </div>
  );

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Desktop Sidebar */}
      <Sidebar />

      {/* Mobile Sidebar */}
      {sidebarOpen && (
        <div className="fixed inset-0 z-50 lg:hidden">
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
          <div className="relative flex flex-col w-64 h-full bg-gray-900">
            <Sidebar mobile />
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between px-4 py-3">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
              >
                <Menu className="h-6 w-6" />
              </button>
              <h1 className="ml-2 text-xl font-semibold text-gray-900">
                Hệ thống quản lý học sinh
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">
                Xin chào, {user?.name}
              </span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {getRoleDisplayName(user?.role || '')}
              </span>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
