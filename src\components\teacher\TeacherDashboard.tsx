'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { Course, PaginatedResponse } from '@/types';
import { formatDate } from '@/lib/utils';
import { coursesApi } from '@/lib/api/courses';
import { classroomsApi } from '@/lib/api/classrooms';
import { enrollmentsApi } from '@/lib/api/enrollments';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { BookOpen, GraduationCap, Calendar, CheckSquare, DollarSign, Plus } from 'lucide-react';
import toast from 'react-hot-toast';

export default function TeacherDashboard() {
  const { user } = useAuth();
  const [courses, setCourses] = useState<PaginatedResponse<Course> | null>(null);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalCourses: 0,
    activeCourses: 0,
    totalClasses: 0,
    activeClasses: 0,
    totalStudents: 0,
    pendingEnrollments: 0,
  });

  useEffect(() => {
    fetchCourses();
  }, []);

  // Update stats when courses data changes
  useEffect(() => {
    fetchStats();
  }, [courses]);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const data = await coursesApi.getCourses(0, 5);
      setCourses(data);
      console.log("data lop học:", data);
    } catch (error) {
      console.error('Error fetching courses:', error);
      toast.error('Không thể tải danh sách khóa học');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Tính toán stats từ courses data
      let courseStats = { totalCourses: 0, activeCourses: 0 };
      if (courses && courses.items) {
        const activeCourses = courses.items.filter(course => course.is_active).length;
        courseStats = {
          totalCourses: courses.total,
          activeCourses: activeCourses,
        };
      }

      // Lấy classroom stats từ API
      let classroomStats = { totalClassrooms: 0, activeClassrooms: 0 };
      try {
        const classroomData = await classroomsApi.getClassroomStats();
        classroomStats = {
          totalClassrooms: classroomData.totalClassrooms || 0,
          activeClassrooms: classroomData.activeClassrooms || 0,
        };
      } catch (error) {
        console.error('Error fetching classroom stats:', error);
      }

      // Lấy enrollment stats từ API
      let enrollmentStats = { totalStudents: 0, pendingEnrollments: 0 };
      try {
        const enrollmentData = await enrollmentsApi.getEnrollmentStats();
        enrollmentStats = {
          totalStudents: enrollmentData.approvedEnrollments || 0,
          pendingEnrollments: enrollmentData.pendingEnrollments || 0,
        };
      } catch (error) {
        console.error('Error fetching enrollment stats:', error);
      }

      setStats({
        totalCourses: courseStats.totalCourses,
        activeCourses: courseStats.activeCourses,
        totalClasses: classroomStats.totalClassrooms,
        activeClasses: classroomStats.activeClassrooms,
        totalStudents: enrollmentStats.totalStudents,
        pendingEnrollments: enrollmentStats.pendingEnrollments,
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Chào mừng, {user?.name}!
        </h1>
        <p className="text-gray-600">
          Quản lý khóa học, lớp học và học sinh của bạn
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BookOpen className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Khóa học</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.activeCourses}/{stats.totalCourses}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <GraduationCap className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Lớp học</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.activeClasses}/{stats.totalClasses}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckSquare className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Đăng ký chờ duyệt</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.pendingEnrollments}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Courses */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Khóa học gần đây</CardTitle>
            <Link href="/teacher/courses/create">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Thêm khóa học
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-500">Đang tải...</p>
            </div>
          ) : courses && courses.items && courses.items.length > 0 ? (
            <div className="space-y-4">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tên khóa học
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Thời gian
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Trạng thái
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Thao tác
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {courses.items.map((course) => (
                      <tr key={course.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{course.name}</div>
                          <div className="text-sm text-gray-500">{course.description || 'Không có mô tả'}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{formatDate(course.start_date)} - {formatDate(course.end_date)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${course.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                            {course.is_active ? 'Đang hoạt động' : 'Không hoạt động'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Link href={`/teacher/courses/${course.id}`}>
                            <Button variant="outline" size="sm">
                              Chi tiết
                            </Button>
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {courses.total > 5 && (
                <div className="text-center pt-4">
                  <Link href="/teacher/courses">
                    <Button variant="outline">
                      Xem tất cả ({courses.total} khóa học)
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Chưa có khóa học</h3>
              <p className="mt-1 text-sm text-gray-500">
                Bắt đầu bằng cách tạo khóa học đầu tiên.
              </p>
              <div className="mt-6">
                <Link href="/teacher/courses/create">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Thêm khóa học
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <Calendar className="h-8 w-8 text-blue-600 mx-auto" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">Lịch học</h3>
              <p className="mt-1 text-sm text-gray-500">
                Quản lý lịch học và điểm danh
              </p>
              <div className="mt-4">
                <Link href="/teacher/classes">
                  <Button variant="outline" className="w-full">
                    Xem lịch học
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <GraduationCap className="h-8 w-8 text-green-600 mx-auto" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">Học sinh</h3>
              <p className="mt-1 text-sm text-gray-500">
                Duyệt đăng ký và quản lý học sinh
              </p>
              <div className="mt-4">
                <Link href="/teacher/enrollments">
                  <Button variant="outline" className="w-full">
                    Xem đăng ký
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <DollarSign className="h-8 w-8 text-purple-600 mx-auto" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">Học phí</h3>
              <p className="mt-1 text-sm text-gray-500">
                Tính học phí và xuất báo cáo
              </p>
              <div className="mt-4">
                <Link href="/teacher/tuition">
                  <Button variant="outline" className="w-full">
                    Quản lý học phí
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
