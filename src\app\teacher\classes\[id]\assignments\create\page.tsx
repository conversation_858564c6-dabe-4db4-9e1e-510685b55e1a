'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { CreateAssignmentForm } from '@/components/assignments';
import { classroomsApi } from '@/lib/api/classrooms';
import Link from 'next/link';

export default function CreateAssignmentPage() {
  const params = useParams();
  const router = useRouter();
  const classroomId = parseInt(params.id as string);
  const { user } = useAuth();
  const [classroom, setClassroom] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchClassroom();
  }, [classroomId]);

  const fetchClassroom = async () => {
    try {
      setLoading(true);
      const data = await classroomsApi.getClassroomById(classroomId);
      setClassroom(data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch classroom');
    } finally {
      setLoading(false);
    }
  };

  const handleSuccess = () => {
    router.push(`/teacher/classes/${classroomId}/assignments`);
  };

  const handleCancel = () => {
    router.push(`/teacher/classes/${classroomId}/assignments`);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !classroom) {
    return (
      <div className="space-y-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error || 'Classroom not found'}</p>
        </div>
        <Link
          href={`/teacher/classes/${classroomId}/assignments`}
          className="text-blue-600 hover:text-blue-800 underline"
        >
          ← Back to Assignments
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-gray-600">
        <Link href="/teacher/classes" className="hover:text-blue-600">
          Classes
        </Link>
        <span>›</span>
        <Link href={`/teacher/classes/${classroomId}`} className="hover:text-blue-600">
          {classroom.name}
        </Link>
        <span>›</span>
        <Link href={`/teacher/classes/${classroomId}/assignments`} className="hover:text-blue-600">
          Assignments
        </Link>
        <span>›</span>
        <span className="text-gray-900">Create New</span>
      </nav>

      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create Assignment</h1>
            <p className="text-gray-600 mt-1">
              {classroom.name} • {classroom.course_name}
            </p>
          </div>
          <Link
            href={`/teacher/classes/${classroomId}/assignments`}
            className="text-gray-600 hover:text-gray-800"
          >
            ← Back to Assignments
          </Link>
        </div>
      </div>

      {/* Create Form */}
      <CreateAssignmentForm
        classroomId={classroomId}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}
