'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
// import DashboardLayout from '@/components/layout/DashboardLayout';
import { Course, PaginatedResponse } from '@/types';
import { formatDate, getStatusColor } from '@/lib/utils';
import { coursesApi } from '@/lib/api/courses';
import Button from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { ArrowLeft, Plus, Search, RefreshCw, Edit, Trash2, BookOpen } from 'lucide-react';
import toast from 'react-hot-toast';

export default function CoursesPage() {
  const router = useRouter();
  const { isTeacher } = useAuth();
  
  const [courses, setCourses] = useState<PaginatedResponse<Course> | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize] = useState(10);

  // Redirect if not teacher
  useEffect(() => {
    if (!isTeacher) {
      router.push('/dashboard');
    }
  }, [isTeacher, router]);

  // Fetch courses on mount and when page/search changes
  useEffect(() => {
    if (isTeacher) {
      fetchCourses();
    }
  }, [isTeacher, currentPage, searchTerm]);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const data = await coursesApi.getCourses(currentPage, pageSize);
      setCourses(data);
    } catch (error) {
      console.error('Error fetching courses:', error);
      toast.error('Không thể tải danh sách khóa học');
    } finally {
      setLoading(false);
    }
  };

  const deleteCourse = async (courseId: number, courseName: string) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa khóa học "${courseName}"?\n\nHành động này sẽ vô hiệu hóa khóa học và không thể hoàn tác.`)) {
      return;
    }

    try {
      await coursesApi.deleteCourse(courseId);
      toast.success('Xóa khóa học thành công');
      fetchCourses(); // Refresh list
    } catch (error) {
      console.error('Error deleting course:', error);
      toast.error('Không thể xóa khóa học');
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(0); // Reset to first page on new search
    fetchCourses();
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  if (!isTeacher) {
    return null;
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div className="flex items-center space-x-2">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Quay lại
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Quản lý khóa học</h1>
              <p className="text-gray-600">Tạo và quản lý các khóa học của bạn</p>
            </div>
          </div>
          <Link href="/teacher/courses/create">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Thêm khóa học
            </Button>
          </Link>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-4">
            <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-2">
              <div className="relative flex-grow">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Tìm kiếm theo tên khóa học..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <Button type="submit" variant="outline">
                <Search className="mr-2 h-4 w-4" />
                Tìm kiếm
              </Button>
              <Button type="button" variant="outline" onClick={() => fetchCourses()}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Làm mới
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Courses List */}
        <Card>
          <CardHeader>
            <CardTitle>Danh sách khóa học</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-500">Đang tải...</p>
              </div>
            ) : courses && courses.items && courses.items.length > 0 ? (
              <div className="space-y-4">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Tên khóa học
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Thời gian
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Trạng thái
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Thao tác
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {courses.items.map((course) => (
                        <tr key={course.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{course.name}</div>
                            <div className="text-sm text-gray-500">{course.description || 'Không có mô tả'}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {formatDate(course.start_date)} - {formatDate(course.end_date)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(course.is_active ? 'active' : 'inactive')}`}>
                              {course.is_active ? 'Đang hoạt động' : 'Không hoạt động'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex items-center justify-end space-x-2">
                              <Link href={`/teacher/courses/${course.id}`}>
                                <Button variant="outline" size="sm">
                                  <BookOpen className="h-4 w-4" />
                                </Button>
                              </Link>
                              
                              <Link href={`/teacher/courses/${course.id}/edit`}>
                                <Button variant="outline" size="sm">
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </Link>

                              <Button
                                variant="danger"
                                size="sm"
                                onClick={() => deleteCourse(course.id, course.name)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {courses.totalPages > 1 && (
                  <div className="flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6">
                    <div className="flex flex-1 justify-between sm:hidden">
                      <Button
                        variant="outline"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 0}
                      >
                        Trước
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage >= courses.totalPages - 1}
                      >
                        Sau
                      </Button>
                    </div>
                    <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                      <div>
                        <p className="text-sm text-gray-700">
                          Hiển thị <span className="font-medium">{currentPage * pageSize + 1}</span> đến{' '}
                          <span className="font-medium">
                            {Math.min((currentPage + 1) * pageSize, courses.total)}
                          </span>{' '}
                          trong tổng số <span className="font-medium">{courses.total}</span> khóa học
                        </p>
                      </div>
                      <div>
                        <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                          <Button
                            variant="outline"
                            className="rounded-l-md"
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 0}
                          >
                            Trước
                          </Button>
                          {Array.from({ length: Math.min(courses.totalPages, 5) }, (_, i) => {
                            const pageNum = currentPage < 3 ? i : currentPage - 2 + i;
                            if (pageNum >= courses.totalPages) return null;
                            return (
                              <Button
                                key={pageNum}
                                variant={pageNum === currentPage ? 'primary' : 'outline'}
                                onClick={() => handlePageChange(pageNum)}
                                className="rounded-none"
                              >
                                {pageNum + 1}
                              </Button>
                            );
                          })}
                          <Button
                            variant="outline"
                            className="rounded-r-md"
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage >= courses.totalPages - 1}
                          >
                            Sau
                          </Button>
                        </nav>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Không tìm thấy khóa học</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm ? 'Không có kết quả phù hợp với tìm kiếm của bạn.' : 'Chưa có khóa học nào. Hãy tạo khóa học đầu tiên.'}
                </p>
                <div className="mt-6">
                  <Link href="/teacher/courses/create">
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Thêm khóa học
                    </Button>
                  </Link>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
  );
}
