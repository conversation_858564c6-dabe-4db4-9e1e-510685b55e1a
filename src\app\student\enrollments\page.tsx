'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
// import DashboardLayout from '@/components/layout/DashboardLayout';
import { Enrollment, PaginatedResponse } from '@/types';
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils';
import Button from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { BookOpen, Clock, CheckCircle, XCircle, Trash2, AlertCircle } from 'lucide-react';
import toast from 'react-hot-toast';
import { enrollmentsApi } from '@/lib/api/enrollments';

export default function StudentEnrollmentsPage() {
  const router = useRouter();
  const { user, isStudent } = useAuth();
  
  const [enrollments, setEnrollments] = useState<PaginatedResponse<Enrollment> | null>(null);
  const [loading, setLoading] = useState(true);
  const [cancelling, setCancelling] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(0);

  // Redirect if not student
  useEffect(() => {
    if (!isStudent) {
      router.push('/dashboard');
    }
  }, [isStudent, router]);

  // Fetch enrollments
  useEffect(() => {
    if (isStudent) {
      fetchEnrollments();
    }
  }, [isStudent, currentPage]);

  const fetchEnrollments = async () => {
    try {
      setLoading(true);

      const response = await apiClient.getStudentEnrollments({
        page: currentPage,
        limit: 10
      });

      setEnrollments(response);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching enrollments:', error);
      toast.error('Không thể tải danh sách đăng ký');
      setLoading(false);
    }
  };

  const handleCancelEnrollment = async (enrollmentId: number) => {
    if (!confirm('Bạn có chắc chắn muốn hủy đăng ký này?')) {
      return;
    }

    try {
      setCancelling(enrollmentId);

      await apiClient.cancelEnrollment(enrollmentId);

      toast.success('Hủy đăng ký thành công!');
      fetchEnrollments(); // Refresh list
      setCancelling(null);
    } catch (error: any) {
      console.error('Cancel enrollment error:', error);
      toast.error(error.message || 'Hủy đăng ký thất bại');
      setCancelling(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Chờ duyệt';
      case 'approved':
        return 'Đã duyệt';
      case 'rejected':
        return 'Bị từ chối';
      default:
        return 'Không xác định';
    }
  };

  if (!isStudent) {
    return null;
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Đăng ký của tôi</h1>
            <p className="text-gray-600">
              Quản lý và theo dõi trạng thái đăng ký lớp học
            </p>
          </div>
        </div>

        {/* Enrollments List */}
        {loading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 rounded-lg h-32"></div>
              </div>
            ))}
          </div>
        ) : enrollments && enrollments.items.length > 0 ? (
          <div className="space-y-4">
            {enrollments.items.map((enrollment) => (
              <Card key={enrollment.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        {getStatusIcon(enrollment.status)}
                        <h3 className="text-lg font-semibold text-gray-900">
                          {enrollment.classroom?.name}
                        </h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(enrollment.status)}`}>
                          {getStatusText(enrollment.status)}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Khóa học:</span>
                          <p>{enrollment.classroom?.course?.name}</p>
                        </div>
                        <div>
                          <span className="font-medium">Giáo viên:</span>
                          <p>{enrollment.classroom?.teacher?.name}</p>
                        </div>
                        <div>
                          <span className="font-medium">Học phí:</span>
                          <p>{formatCurrency(parseFloat(enrollment.classroom?.fee_per_session || '0'))}/buổi</p>
                        </div>
                        <div>
                          <span className="font-medium">Ngày đăng ký:</span>
                          <p>{formatDate(enrollment.enrolled_at)}</p>
                        </div>
                      </div>

                      {enrollment.classroom?.start_date && enrollment.classroom?.end_date && (
                        <div className="mt-2 text-sm text-gray-600">
                          <span className="font-medium">Thời gian học:</span>
                          <span className="ml-1">
                            {formatDate(enrollment.classroom.start_date)} - {formatDate(enrollment.classroom.end_date)}
                          </span>
                        </div>
                      )}

                      {enrollment.approved_at && (
                        <div className="mt-2 text-sm text-gray-600">
                          <span className="font-medium">Ngày duyệt:</span>
                          <span className="ml-1">{formatDate(enrollment.approved_at)}</span>
                        </div>
                      )}

                      {enrollment.notes && (
                        <div className="mt-3 p-3 bg-blue-50 rounded-md">
                          <span className="text-sm font-medium text-blue-900">Ghi chú từ giáo viên:</span>
                          <p className="text-sm text-blue-700 mt-1">{enrollment.notes}</p>
                        </div>
                      )}
                    </div>

                    <div className="ml-4">
                      {enrollment.status === 'pending' && (
                        <Button
                          variant="danger"
                          size="sm"
                          onClick={() => handleCancelEnrollment(enrollment.id)}
                          loading={cancelling === enrollment.id}
                          disabled={cancelling === enrollment.id}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          {cancelling === enrollment.id ? 'Đang hủy...' : 'Hủy đăng ký'}
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                Chưa có đăng ký nào
              </h3>
              <p className="mt-2 text-gray-600">
                Bạn chưa đăng ký lớp học nào. Hãy tìm và đăng ký lớp học phù hợp.
              </p>
              <div className="mt-6">
                <Button onClick={() => router.push('/student/classes')}>
                  <BookOpen className="mr-2 h-4 w-4" />
                  Tìm lớp học
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
