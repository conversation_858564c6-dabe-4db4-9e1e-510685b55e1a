'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { Enrollment, PaginatedResponse } from '@/types';
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils';
import Button from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { BookOpen, Clock, CheckCircle, XCircle, Trash2, AlertCircle } from 'lucide-react';
import toast from 'react-hot-toast';
import { enrollmentsApi } from '@/lib/api/enrollments';

export default function StudentEnrollmentsPage() {
  const router = useRouter();
  const { user, isStudent } = useAuth();
  
  const [enrollments, setEnrollments] = useState<PaginatedResponse<Enrollment> | null>(null);
  const [loading, setLoading] = useState(true);
  const [cancelling, setCancelling] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(0);

  // Redirect if not student
  useEffect(() => {
    if (!isStudent) {
      router.push('/dashboard');
    }
  }, [isStudent, router]);

  // Fetch enrollments
  useEffect(() => {
    if (isStudent) {
      fetchEnrollments();
    }
  }, [isStudent, currentPage]);

  const fetchEnrollments = async () => {
    try {
      setLoading(true);
      const response = await enrollmentsApi.getStudentEnrollments(currentPage, 10);
      setEnrollments(response);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching enrollments:', error);
      toast.error('Không thể tải danh sách đăng ký');
      setLoading(false);
    }
  };

  const handleCancelEnrollment = async (enrollmentId: number) => {
    if (!confirm('Bạn có chắc chắn muốn hủy đăng ký này?')) {
      return;
    }

    try {
      setCancelling(enrollmentId);
      await enrollmentsApi.cancelEnrollment(enrollmentId);
      toast.success('Hủy đăng ký thành công!');
      fetchEnrollments(); // Refresh list
      setCancelling(null);
    } catch (error: any) {
      console.error('Cancel enrollment error:', error);
      toast.error(error.message || 'Hủy đăng ký thất bại');
      setCancelling(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Chờ duyệt';
      case 'approved':
        return 'Đã duyệt';
      case 'rejected':
        return 'Bị từ chối';
      default:
        return 'Không xác định';
    }
  };

  if (!isStudent) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Đăng ký của tôi</h1>
          <p className="text-gray-600">
            Quản lý và theo dõi trạng thái đăng ký lớp học
          </p>
        </div>
      </div>

      {/* Enrollments List */}
      {loading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : enrollments?.items?.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Chưa có đăng ký nào
            </h3>
            <p className="text-gray-600 mb-6">
              Bạn chưa đăng ký lớp học nào. Hãy tìm và đăng ký lớp học phù hợp.
            </p>
            <Button onClick={() => router.push('/student/classrooms')}>
              Tìm lớp học
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {enrollments?.items?.map((enrollment) => (
            <Card key={enrollment.id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {enrollment.classroom?.name}
                      </h3>
                      <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-sm ${getStatusColor(enrollment.status)}`}>
                        {getStatusIcon(enrollment.status)}
                        <span>{getStatusText(enrollment.status)}</span>
                      </div>
                    </div>
                    
                    <p className="text-gray-600 mb-3">
                      {enrollment.classroom?.course?.name} - Lớp {enrollment.classroom?.grade}
                    </p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Ngày đăng ký:</span>
                        <p className="font-medium">{formatDate(enrollment.enrolled_at)}</p>
                      </div>
                      
                      {enrollment.approved_at && (
                        <div>
                          <span className="text-gray-500">Ngày duyệt:</span>
                          <p className="font-medium">{formatDate(enrollment.approved_at)}</p>
                        </div>
                      )}
                      
                      <div>
                        <span className="text-gray-500">Học phí/buổi:</span>
                        <p className="font-medium">{formatCurrency(enrollment.classroom?.fee_per_session || 0)}</p>
                      </div>
                    </div>

                    {enrollment.notes && (
                      <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                        <span className="text-gray-500 text-sm">Ghi chú:</span>
                        <p className="text-gray-700 text-sm mt-1">{enrollment.notes}</p>
                      </div>
                    )}
                  </div>

                  {enrollment.status === 'pending' && (
                    <div className="ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCancelEnrollment(enrollment.id)}
                        disabled={cancelling === enrollment.id}
                        className="text-red-600 border-red-200 hover:bg-red-50"
                      >
                        {cancelling === enrollment.id ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                            Đang hủy...
                          </>
                        ) : (
                          <>
                            <Trash2 className="h-4 w-4 mr-2" />
                            Hủy đăng ký
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {enrollments && enrollments.totalPages > 1 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Trang {enrollments.page + 1} / {enrollments.totalPages}
                ({enrollments.total} đăng ký)
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={!enrollments.hasPrevPage}
                >
                  Trước
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={!enrollments.hasNextPage}
                >
                  Sau
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
