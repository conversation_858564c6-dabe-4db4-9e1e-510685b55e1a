'use client';

import React, { useState, useEffect } from 'react';
import { Assignment, assignmentsApi } from '@/lib/api/assignments';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';
import AssignmentStats from './AssignmentStats';
import SolutionFiles from './SolutionFiles';
import StudentSubmission from './StudentSubmission';
import QuestionFiles from './QuestionFiles';
import FileUpload from './FileUpload';

interface AssignmentDetailProps {
  assignmentId: number;
}

export default function AssignmentDetail({ assignmentId }: AssignmentDetailProps) {
  const [assignment, setAssignment] = useState<Assignment | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showQuestionUpload, setShowQuestionUpload] = useState(false);
  const [showSolutionUpload, setShowSolutionUpload] = useState(false);

  const { user } = useAuth();

  useEffect(() => {
    fetchAssignment();
  }, [assignmentId]);

  const fetchAssignment = async () => {
    try {
      setLoading(true);
      const data = await assignmentsApi.getAssignment(assignmentId);
      setAssignment(data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch assignment');
    } finally {
      setLoading(false);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'homework': return '📝';
      case 'exam': return '📋';
      case 'quiz': return '❓';
      default: return '📄';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !assignment) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600">{error || 'Assignment not found'}</p>
        <button
          onClick={fetchAssignment}
          className="mt-2 text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <span className="text-2xl">{getTypeIcon(assignment.type)}</span>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{assignment.title}</h1>
              <p className="text-gray-600">
                {assignment.classroom_name} • {assignment.teacher_name}
              </p>
            </div>
          </div>
        </div>

        {/* Assignment Info */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Type</h3>
            <p className="mt-1 text-sm text-gray-900 capitalize">{assignment.type}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Deadline</h3>
            <p className="mt-1 text-sm text-gray-900">
              {new Date(assignment.deadline).toLocaleString()}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Max Score</h3>
            <p className="mt-1 text-sm text-gray-900">{assignment.max_score} points</p>
          </div>
        </div>
      </div>

      {/* Description */}
      {assignment.description && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">Mô tả</h2>
          <p className="text-gray-700 whitespace-pre-wrap">{assignment.description}</p>
        </div>
      )}

      {/* Instructions */}
      {assignment.instructions && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">Hướng dẫn</h2>
          <p className="text-gray-700 whitespace-pre-wrap">{assignment.instructions}</p>
        </div>
      )}

      {/* Question Files */}
      <div className="bg-white rounded-lg shadow-md p-6">
        {assignment.question_files && assignment.question_files.length > 0 ? (
          <QuestionFiles
            files={assignment.question_files}
            canEdit={user?.role === 'teacher'}
            onDelete={(fileIndex) => {
              // TODO: Implement delete functionality
              console.log('Delete question file at index:', fileIndex);
            }}
          />
        ) : (
          <div className="text-center py-8">
            <div className="text-gray-400 text-6xl mb-4">📄</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Chưa có đề bài</h3>
            <p className="text-gray-600 mb-4">
              {user?.role === 'teacher'
                ? 'Tải lên đề bài để học sinh có thể xem và làm bài.'
                : 'Giáo viên chưa tải lên đề bài cho bài tập này.'
              }
            </p>
            {user?.role === 'teacher' && (
              <button
                onClick={() => setShowQuestionUpload(true)}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                📤 Tải lên đề bài
              </button>
            )}
          </div>
        )}

        {/* Upload button for teachers when files exist */}
        {user?.role === 'teacher' && assignment.question_files && assignment.question_files.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <button
              onClick={() => setShowQuestionUpload(true)}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              📤 Thêm đề bài
            </button>
          </div>
        )}
      </div>

      {/* Assignment Statistics - Teacher only */}
      {user?.role === 'teacher' && (
        <AssignmentStats
          assignmentId={assignment.id}
        />
      )}

      {/* Student Submission */}
      {user?.role === 'student' && (
        <StudentSubmission
          assignment={assignment}
          onUpdate={fetchAssignment}
        />
      )}

      {/* Solution Files */}
      <SolutionFiles
        assignment={assignment}
        onUpdate={fetchAssignment}
      />

      {/* Teacher Actions */}
      {user?.role === 'teacher' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Quản lý bài tập</h2>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <Link
                href={`/teacher/assignments/${assignment.id}/submissions`}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Xem bài nộp ({assignment.submission_count || 0})
              </Link>
              <Link
                href={`/teacher/assignments/${assignment.id}/edit`}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                Chỉnh sửa bài tập
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Question Upload Modal */}
      {showQuestionUpload && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-semibold mb-4">Tải lên đề bài</h2>
            <FileUpload
              assignmentId={assignment.id}
              uploadType="question"
              onSuccess={() => {
                setShowQuestionUpload(false);
                fetchAssignment();
              }}
              onCancel={() => setShowQuestionUpload(false)}
            />
          </div>
        </div>
      )}

      {/* Solution Upload Modal */}
      {showSolutionUpload && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-semibold mb-4">Tải lên đáp án</h2>
            <FileUpload
              assignmentId={assignment.id}
              uploadType="solution"
              onSuccess={() => {
                setShowSolutionUpload(false);
                fetchAssignment();
              }}
              onCancel={() => setShowSolutionUpload(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}