'use client';

import React, { useState, useEffect } from 'react';
import { assignmentsApi } from '@/lib/api/assignments';
import { <PERSON>, Card<PERSON>ead<PERSON>, CardT<PERSON>le, CardContent } from '@/components/ui/Card';
import { Users, CheckCircle, Clock, XCircle, FileText, Download } from 'lucide-react';

interface AssignmentStatsProps {
  assignmentId: number;
}

interface SubmissionStats {
  total: number;
  submitted: number;
  graded: number;
  pending: number;
  late: number;
  missing: number;
}

export default function AssignmentStats({ assignmentId }: AssignmentStatsProps) {
  const [stats, setStats] = useState<SubmissionStats>({
    total: 0,
    submitted: 0,
    graded: 0,
    pending: 0,
    late: 0,
    missing: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStats();
  }, [assignmentId]);

  const fetchStats = async () => {
    try {
      setLoading(true);

      // Fetch assignment details to get total students
      const assignmentDetail = await assignmentsApi.getAssignment(assignmentId);
      const totalStudentsInClass = assignmentDetail.total_students || 0;

      // Fetch all submissions to calculate stats
      const response = await assignmentsApi.getAssignmentSubmissions(assignmentId, 0, 1000);
      const submissions = response.data;

      const submissionStats = {
        total: totalStudentsInClass,
        submitted: submissions.length,
        graded: submissions.filter(s => s.score !== null && s.score !== undefined).length,
        pending: submissions.filter(s => s.score === null || s.score === undefined).length,
        late: submissions.filter(s => s.is_late).length,
        missing: Math.max(0, totalStudentsInClass - submissions.length)
      };

      console.log("Assignment detail:", assignmentDetail);
      console.log("Total students in class:", totalStudentsInClass);
      console.log("Submissions data:", submissions);
      console.log("Calculated stats:", submissionStats);

      setStats(submissionStats);
    } catch (error) {
      console.error('Error fetching assignment stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const submissionRate = stats.total > 0 ? (stats.submitted / stats.total * 100).toFixed(1) : '0';
  const gradingRate = stats.submitted > 0 ? (stats.graded / stats.submitted * 100).toFixed(1) : '0';

  return (
    <div className="space-y-4">
      {/* Main Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Tổng học sinh</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Đã nộp</p>
                <p className="text-2xl font-bold text-gray-900">{stats.submitted}</p>
                <p className="text-xs text-gray-500">{submissionRate}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Đã chấm</p>
                <p className="text-2xl font-bold text-gray-900">{stats.graded}</p>
                <p className="text-xs text-gray-500">{gradingRate}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Chờ chấm</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Nộp muộn</p>
                <p className="text-2xl font-bold text-gray-900">{stats.late}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-gray-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Chưa nộp</p>
                <p className="text-2xl font-bold text-gray-900">{stats.missing}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progress Bars */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Tiến độ bài tập</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Submission Progress */}
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Tỷ lệ nộp bài</span>
                <span>{submissionRate}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${submissionRate}%` }}
                ></div>
              </div>
            </div>

            {/* Grading Progress */}
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Tiến độ chấm bài</span>
                <span>{gradingRate}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${gradingRate}%` }}
                ></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
