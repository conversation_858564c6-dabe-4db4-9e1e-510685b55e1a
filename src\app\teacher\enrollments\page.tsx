'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Enrollment, PaginatedResponse } from '@/types';
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { CheckCircle, XCircle, Clock, Users, BookOpen, MessageSquare } from 'lucide-react';
import toast from 'react-hot-toast';
import { enrollmentsApi } from '@/lib/api/enrollments';

export default function TeacherEnrollmentsPage() {
  const router = useRouter();
  const { isTeacher } = useAuth();
  
  const [enrollments, setEnrollments] = useState<PaginatedResponse<Enrollment> | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [selectedEnrollment, setSelectedEnrollment] = useState<Enrollment | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [notes, setNotes] = useState('');

  // Redirect if not teacher
  useEffect(() => {
    if (!isTeacher) {
      router.push('/dashboard');
    }
  }, [isTeacher, router]);

  // Fetch enrollments
  useEffect(() => {
    if (isTeacher) {
      fetchEnrollments();
    }
  }, [isTeacher, currentPage, statusFilter]);

  const fetchEnrollments = async () => {
    try {
      setLoading(true);
      
      const response = await enrollmentsApi.getEnrollments(currentPage, 10);
      
      setEnrollments(response);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching enrollments:', error);
      toast.error('Không thể tải danh sách đăng ký');
      setLoading(false);
    }
  };

  const handleApprove = (enrollment: Enrollment) => {
    setSelectedEnrollment(enrollment);
    setNotes('');
    setShowModal(true);
  };

  const handleReject = (enrollment: Enrollment) => {
    setSelectedEnrollment(enrollment);
    setNotes('');
    setShowModal(true);
  };

  const handleSubmitDecision = async (status: 'approved' | 'rejected') => {
    if (!selectedEnrollment) return;

    try {
      setProcessing(selectedEnrollment.id);
      
      await enrollmentsApi.updateEnrollmentStatus(selectedEnrollment.id, status, notes.trim() || undefined);
      
      toast.success(`${status === 'approved' ? 'Duyệt' : 'Từ chối'} đăng ký thành công!`);
      setShowModal(false);
      setSelectedEnrollment(null);
      setNotes('');
      fetchEnrollments(); // Refresh list
      setProcessing(null);
    } catch (error: any) {
      console.error('Update enrollment error:', error);
      toast.error(error.message || 'Cập nhật thất bại');
      setProcessing(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Chờ duyệt';
      case 'approved':
        return 'Đã duyệt';
      case 'rejected':
        return 'Đã từ chối';
      default:
        return 'Không xác định';
    }
  };

  if (!isTeacher) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Quản lý đăng ký</h1>
            <p className="text-gray-600">
              Duyệt và quản lý đăng ký học sinh vào lớp học
            </p>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Lọc theo trạng thái
                </label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Tất cả trạng thái</option>
                  <option value="pending">Chờ duyệt</option>
                  <option value="approved">Đã duyệt</option>
                  <option value="rejected">Đã từ chối</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enrollments List */}
        {loading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 rounded-lg h-32"></div>
              </div>
            ))}
          </div>
        ) : enrollments && enrollments.items.length > 0 ? (
          <div className="space-y-4">
            {enrollments.items.map((enrollment) => (
              <Card key={enrollment.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        {getStatusIcon(enrollment.status)}
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">
                            {enrollment.student?.name}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {enrollment.student?.email} • Lớp {enrollment.student?.grade}
                          </p>
                        </div>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(enrollment.status)}`}>
                          {getStatusText(enrollment.status)}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                        <div className="flex items-center">
                          <BookOpen className="h-4 w-4 mr-2" />
                          <div>
                            <span className="font-medium">Lớp học:</span>
                            <p>{enrollment.classroom?.name}</p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <Users className="h-4 w-4 mr-2" />
                          <div>
                            <span className="font-medium">Khóa học:</span>
                            <p>{enrollment.classroom?.course?.name}</p>
                          </div>
                        </div>
                        <div>
                          <span className="font-medium">Ngày đăng ký:</span>
                          <p>{formatDate(enrollment.enrolled_at)}</p>
                        </div>
                      </div>

                      {enrollment.approved_at && (
                        <div className="text-sm text-gray-600 mb-2">
                          <span className="font-medium">Ngày duyệt:</span>
                          <span className="ml-1">{formatDate(enrollment.approved_at)}</span>
                        </div>
                      )}

                      {enrollment.notes && (
                        <div className="mt-3 p-3 bg-gray-50 rounded-md">
                          <div className="flex items-center mb-1">
                            <MessageSquare className="h-4 w-4 text-gray-500 mr-1" />
                            <span className="text-sm font-medium text-gray-700">Ghi chú:</span>
                          </div>
                          <p className="text-sm text-gray-600">{enrollment.notes}</p>
                        </div>
                      )}
                    </div>

                    <div className="ml-4">
                      {enrollment.status === 'pending' && (
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleReject(enrollment)}
                            disabled={processing === enrollment.id}
                          >
                            <XCircle className="h-4 w-4 mr-1" />
                            Từ chối
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleApprove(enrollment)}
                            disabled={processing === enrollment.id}
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Duyệt
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                Không có đăng ký nào
              </h3>
              <p className="mt-2 text-gray-600">
                {statusFilter 
                  ? `Không có đăng ký nào với trạng thái "${getStatusText(statusFilter)}"`
                  : 'Chưa có học sinh nào đăng ký vào lớp học của bạn'
                }
              </p>
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {enrollments && enrollments.totalPages > 1 && (
          <div className="flex justify-center space-x-2">
            <Button
              variant="outline"
              disabled={!enrollments.hasPrevPage}
              onClick={() => setCurrentPage(currentPage - 1)}
            >
              Trước
            </Button>
            <span className="flex items-center px-4 py-2 text-sm text-gray-700">
              Trang {currentPage + 1} / {enrollments.totalPages}
            </span>
            <Button
              variant="outline"
              disabled={!enrollments.hasNextPage}
              onClick={() => setCurrentPage(currentPage + 1)}
            >
              Sau
            </Button>
          </div>
        )}
      </div>

      {/* Decision Modal */}
      {showModal && selectedEnrollment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Quyết định đăng ký
            </h3>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">
                <strong>Học sinh:</strong> {selectedEnrollment.student?.name}
              </p>
              <p className="text-sm text-gray-600 mb-2">
                <strong>Lớp học:</strong> {selectedEnrollment.classroom?.name}
              </p>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ghi chú (tùy chọn)
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="flex w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Nhập ghi chú cho học sinh..."
              />
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  setShowModal(false);
                  setSelectedEnrollment(null);
                  setNotes('');
                }}
                disabled={processing === selectedEnrollment.id}
              >
                Hủy
              </Button>
              <Button
                variant="danger"
                onClick={() => handleSubmitDecision('rejected')}
                loading={processing === selectedEnrollment.id}
                disabled={processing === selectedEnrollment.id}
              >
                <XCircle className="h-4 w-4 mr-1" />
                Từ chối
              </Button>
              <Button
                onClick={() => handleSubmitDecision('approved')}
                loading={processing === selectedEnrollment.id}
                disabled={processing === selectedEnrollment.id}
              >
                <CheckCircle className="h-4 w-4 mr-1" />
                Duyệt
              </Button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
