'use client';

import React, { useState } from 'react';
import { FileInfo } from '@/lib/api/assignments';
import { X, Download, ZoomIn, ZoomOut, RotateCw } from 'lucide-react';
import Button from '@/components/ui/Button';

interface ImageViewerProps {
  file: FileInfo;
  isOpen: boolean;
  onClose: () => void;
}

export default function ImageViewer({ file, isOpen, onClose }: ImageViewerProps) {
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);

  if (!isOpen) return null;

  // Get the correct file URL (Cloudinary or local)
  const getFileUrl = () => {
    // If file has a direct URL (Cloudinary), use it
    if (file.url && file.url.startsWith('http')) {
      return file.url;
    }
    // Otherwise use local API endpoint
    return `${process.env.NEXT_PUBLIC_API_URL}${file.url}`;
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = getFileUrl();
    link.download = file.originalName || file.originalname;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.25, 3));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.25, 0.25));
  const handleRotate = () => setRotation(prev => (prev + 90) % 360);

  const resetView = () => {
    setZoom(1);
    setRotation(0);
  };

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center">
      <div className="relative max-w-7xl max-h-full w-full h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-white">
          <div className="flex items-center gap-4">
            <h3 className="text-lg font-semibold text-gray-900">
              {file.originalName || file.originalname}
            </h3>
            <div className="flex items-center gap-2">
              <Button onClick={handleZoomOut} variant="outline" size="sm">
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-sm text-gray-600 min-w-[60px] text-center">
                {Math.round(zoom * 100)}%
              </span>
              <Button onClick={handleZoomIn} variant="outline" size="sm">
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button onClick={handleRotate} variant="outline" size="sm">
                <RotateCw className="h-4 w-4" />
              </Button>
              <Button onClick={resetView} variant="outline" size="sm">
                Reset
              </Button>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button onClick={handleDownload} variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Tải về
            </Button>
            <Button onClick={onClose} variant="outline" size="sm">
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Image Container */}
        <div className="flex-1 overflow-auto bg-gray-100 flex items-center justify-center p-4">
          <div 
            className="transition-transform duration-200 ease-in-out"
            style={{
              transform: `scale(${zoom}) rotate(${rotation}deg)`,
              transformOrigin: 'center'
            }}
          >
            <img
              src={getFileUrl()}
              alt={file.originalName || file.originalname}
              className="max-w-none h-auto shadow-lg"
              style={{
                maxHeight: 'calc(100vh - 200px)',
                maxWidth: 'calc(100vw - 100px)'
              }}
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                // Show error message instead of broken image
                const errorDiv = document.createElement('div');
                errorDiv.className = 'flex items-center justify-center h-64 bg-gray-100 text-gray-500';
                errorDiv.textContent = 'Không thể tải ảnh';
                target.parentNode?.appendChild(errorDiv);
              }}
            />
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 bg-white border-t">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div>
              Kích thước: {file.size ? `${(file.size / 1024).toFixed(1)} KB` : 'Unknown'}
            </div>
            <div>
              Nhấn ESC để đóng
            </div>
          </div>
        </div>
      </div>

      {/* Click outside to close */}
      <div 
        className="absolute inset-0 -z-10"
        onClick={onClose}
      />
    </div>
  );
}

// Hook for keyboard shortcuts
export function useImageViewerKeyboard(isOpen: boolean, onClose: () => void) {
  React.useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);
}
