'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { enrollmentsApi } from '@/lib/api/enrollments';
// import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { BookOpen, Users, Clock, Calendar, FileText } from 'lucide-react';
import Link from 'next/link';

interface EnrolledClassroom {
  id: number;
  name: string;
  course_name: string;
  teacher_name: string;
  teacher_id: number;
  description?: string;
  schedule?: string;
  student_count: number;
  status: string;
  enrollment_status: string;
  enrollment_date: string;
}

export default function StudentMyClassesPage() {
  const router = useRouter();
  const { user, isStudent } = useAuth();
  
  const [enrolledClasses, setEnrolledClasses] = useState<EnrolledClassroom[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isStudent) {
      router.push('/login');
      return;
    }
    fetchEnrolledClasses();
  }, [isStudent, router]);

  const fetchEnrolledClasses = async () => {
    try {
      setLoading(true);
      console.log('🔍 Fetching enrolled classes...');
      const response = await enrollmentsApi.getMyEnrollments();
      console.log('🔍 Enrollments API Response:', response);

      // Response should now be an array of enrollments
      const enrollments = Array.isArray(response) ? response : [];
      console.log('🔍 Enrollments array:', enrollments);

      // Filter only approved enrollments (temporarily show all for debug)
      const approvedClasses = enrollments
      // .filter((enrollment: any) => enrollment.status === 'approved')
      .map((enrollment: any) => ({
        id: enrollment.classroom.id,
        name: enrollment.classroom.name,
        course_name: enrollment.course?.name || 'Unknown Course',
        teacher_name: enrollment.teacher?.name || 'Unknown Teacher',
        teacher_id: enrollment.teacher?.id,
        description: enrollment.classroom.description,
        schedule: enrollment.classroom.schedule,
        student_count: 0, // Will need separate API call for this
        status: 'active', // Default status
        enrollment_status: enrollment.status,
        enrollment_date: enrollment.created_at
      }));

      console.log('🔍 Approved classes:', approvedClasses);
      setEnrolledClasses(approvedClasses);
      setError(null);
    } catch (err: any) {
      console.error('❌ Enrollments API Error:', err);
      setError(err.response?.data?.message || 'Failed to fetch enrolled classes');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600">{error}</p>
        <button
          onClick={fetchEnrolledClasses}
          className="mt-2 text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-purple-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-8 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">My Classes</h1>
                <p className="text-gray-600 mt-1 text-lg">
                  You are enrolled in <span className="font-semibold text-indigo-600">{enrolledClasses.length}</span> classes
                </p>
              </div>
            </div>
            <Link
              href="/student/classes"
              className="inline-flex items-center px-6 py-3 border border-transparent rounded-xl text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              Browse More Classes
            </Link>
          </div>
        </div>

      {/* Classes Grid */}
      {enrolledClasses.length === 0 ? (
        <div className="text-center py-12">
          <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No enrolled classes</h3>
          <p className="mt-1 text-sm text-gray-500">
            You haven't enrolled in any classes yet.
          </p>
          <div className="mt-6">
            <Link
              href="/student/classes"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <BookOpen className="mr-2 h-4 w-4" />
              Browse Available Classes
            </Link>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {enrolledClasses.map((classroom) => (
            <Card key={classroom.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg font-semibold text-gray-900 mb-1">
                      {classroom.name}
                    </CardTitle>
                    <p className="text-sm text-gray-600">{classroom.course_name}</p>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    classroom.status === 'active' 
                      ? 'bg-green-100 text-green-600' 
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {classroom.status === 'active' ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Teacher Info */}
                <div className="flex items-center text-sm text-gray-600">
                  <Users className="mr-2 h-4 w-4" />
                  <span>Teacher: {classroom.teacher_name}</span>
                </div>

                {/* Student Count */}
                <div className="flex items-center text-sm text-gray-600">
                  <Users className="mr-2 h-4 w-4" />
                  <span>{classroom.student_count} students enrolled</span>
                </div>

                {/* Schedule */}
                {classroom.schedule && (
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="mr-2 h-4 w-4" />
                    <span>{classroom.schedule}</span>
                  </div>
                )}

                {/* Enrollment Date */}
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="mr-2 h-4 w-4" />
                  <span>Enrolled: {new Date(classroom.enrollment_date).toLocaleDateString()}</span>
                </div>

                {/* Description */}
                {classroom.description && (
                  <p className="text-sm text-gray-700 line-clamp-2">
                    {classroom.description}
                  </p>
                )}

                {/* Action Buttons */}
                <div className="flex gap-2 pt-4">
                  <Link
                    href={`/student/classes/${classroom.id}`}
                    className="flex-1 bg-blue-600 text-white text-center py-2 px-3 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                  >
                    View Details
                  </Link>
                  <Link
                    href={`/student/classes/${classroom.id}/assignments`}
                    className="flex-1 bg-gray-100 text-gray-700 text-center py-2 px-3 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors flex items-center justify-center"
                  >
                    <FileText className="mr-1 h-4 w-4" />
                    Assignments
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
      </div>
    </div>
  );
}
