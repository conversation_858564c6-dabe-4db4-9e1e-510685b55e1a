'use client';

import React, { useState } from 'react';
import { FileInfo } from '@/lib/api/assignments';
import { Download, Eye, FileText, Image, Trash2 } from 'lucide-react';
import ImageViewer from './ImageViewer';

interface QuestionFilesProps {
  files: FileInfo[];
  canEdit?: boolean;
  onDelete?: (fileIndex: number) => void;
}

export default function QuestionFiles({ files, canEdit = false, onDelete }: QuestionFilesProps) {
  const [selectedImageFile, setSelectedImageFile] = useState<FileInfo | null>(null);

  if (!files || files.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <FileText className="mx-auto h-12 w-12 text-gray-300 mb-2" />
        <p>Chưa có đề bài nào được tải lên</p>
      </div>
    );
  }

  const isImageFile = (mimetype: string) => {
    return mimetype.startsWith('image/');
  };

  const isPdfFile = (mimetype: string) => {
    return mimetype === 'application/pdf';
  };

  const getFileIcon = (mimetype: string) => {
    if (isImageFile(mimetype)) {
      return <Image className="h-5 w-5 text-blue-600" />;
    }
    return <FileText className="h-5 w-5 text-red-600" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleDownload = (file: FileInfo) => {
    const link = document.createElement('a');
    link.href = `${process.env.NEXT_PUBLIC_API_URL}${file.url}`;
    link.download = file.originalname || file.filename;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleView = (file: FileInfo) => {
    if (isImageFile(file.mimetype)) {
      setSelectedImageFile(file);
    } else {
      // For PDF and other files, open in new tab
      window.open(`${process.env.NEXT_PUBLIC_API_URL}${file.url}`, '_blank');
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Đề bài</h3>
        <span className="text-sm text-gray-500">{files.length} file(s)</span>
      </div>

      <div className="grid gap-3">
        {files.map((file, index) => (
          <div
            key={index}
            className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
          >
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              {getFileIcon(file.mimetype)}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {file.originalname || file.filename}
                </p>
                <p className="text-xs text-gray-500">
                  {formatFileSize(file.size)} • {file.mimetype}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* View button for images, Download for others */}
              {isImageFile(file.mimetype) ? (
                <button
                  onClick={() => handleView(file)}
                  className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  title="Xem ảnh"
                >
                  <Eye className="h-4 w-4" />
                </button>
              ) : (
                <button
                  onClick={() => handleDownload(file)}
                  className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                  title="Tải xuống"
                >
                  <Download className="h-4 w-4" />
                </button>
              )}

              {/* Always show download option */}
              <button
                onClick={() => handleDownload(file)}
                className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                title="Tải xuống"
              >
                <Download className="h-4 w-4" />
              </button>

              {/* Delete button for teachers */}
              {canEdit && onDelete && (
                <button
                  onClick={() => onDelete(index)}
                  className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  title="Xóa file"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Image Viewer Modal */}
      {selectedImageFile && (
        <ImageViewer
          file={selectedImageFile}
          isOpen={true}
          onClose={() => setSelectedImageFile(null)}
        />
      )}

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <p className="text-blue-800 text-sm">
          <strong>Hướng dẫn:</strong>
        </p>
        <ul className="text-blue-700 text-sm mt-1 space-y-1">
          <li>• Nhấn vào biểu tượng mắt để xem trực tiếp hình ảnh</li>
          <li>• Nhấn vào biểu tượng tải xuống để tải file về máy</li>
          <li>• File PDF sẽ mở trong tab mới để xem</li>
          <li>• Hình ảnh có thể xem trực tiếp mà không cần tải xuống</li>
        </ul>
      </div>
    </div>
  );
}
