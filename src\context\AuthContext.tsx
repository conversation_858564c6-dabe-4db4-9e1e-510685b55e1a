'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, LoginRequest, RegisterRequest } from '@/types';
import { authApi } from '@/lib/api/auth';
import toast from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (data: LoginRequest) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isTeacher: boolean;
  isStudent: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);

  // Simple auth check on mount - ONLY ONCE
  useEffect(() => {
    if (initialized) return; // Prevent multiple calls

    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('token');
        console.log('🔍 Initial auth check, token exists:', !!token);
        if (token) {
          console.log('📞 Calling getProfile API...');
          const userData = await authApi.getProfile();
          console.log('✅ Profile data received:', userData);
          setUser(userData);
        } else {
          console.log('❌ No token found');
          setUser(null);
        }
      } catch (error) {
        console.error('❌ Auth check failed:', error);
        localStorage.removeItem('token');
        setUser(null);
      } finally {
        console.log('✅ Auth check completed');
        setLoading(false);
        setInitialized(true);
      }
    };

    checkAuth();
  }, [initialized]);

  const login = async (data: LoginRequest) => {
    try {
      setLoading(true);
      const response = await authApi.login(data);
      // Store token from response data structure
      if (response.data && response.data.token) {
        localStorage.setItem('token', response.data.token);
        setUser(response.data.user);
        toast.success('Đăng nhập thành công!');
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Đăng nhập thất bại';
      toast.error(message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (data: RegisterRequest) => {
    try {
      setLoading(true);
      await authApi.register(data);
      toast.success('Đăng ký thành công! Vui lòng đăng nhập.');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Đăng ký thất bại';
      toast.error(message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await authApi.logout();
      localStorage.removeItem('token');
      setUser(null);
      setInitialized(false); // Reset auth check status
      toast.success('Đăng xuất thành công!');
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear user state even if API call fails
      localStorage.removeItem('token');
      setUser(null);
      setInitialized(false); // Reset auth check status
    }
  };

  const updateProfile = async (data: Partial<User>) => {
    try {
      const updatedUser = await authApi.getProfile(); // For now, just refetch profile
      setUser(updatedUser);
      toast.success('Cập nhật thông tin thành công!');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Cập nhật thất bại';
      toast.error(message);
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    register,
    logout,
    updateProfile,
    isAuthenticated: !!user,
    isAdmin: user?.role === 'admin',
    isTeacher: user?.role === 'teacher',
    isStudent: user?.role === 'student',
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthContext;
