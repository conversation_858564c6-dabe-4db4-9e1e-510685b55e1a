import { apiClient } from './client';

export interface StudentAttendance {
  student_id: number;
  student_name: string;
  student_email: string;
  student_grade?: string;
  attendance_id: number | null;
  attendance_status: 'present' | 'absent' | null;
  attendance_notes: string | null;
  marked_at: string | null;
  is_marked: boolean;
}

export interface SessionAttendanceResponse {
  session: {
    id: number;
    date: string;
    start_time: string;
    end_time: string;
    title?: string;
    status: string;
    classroom_id: number;
    classroom_name: string;
  };
  students: StudentAttendance[];
  total_students: number;
  marked_students: number;
  present_count: number;
  absent_count: number;
  can_take_attendance: boolean;
}

export interface AttendanceRecord {
  student_id: number;
  status: 'present' | 'absent';
  notes?: string;
}

export interface SaveAttendanceRequest {
  attendance_data: AttendanceRecord[];
}

export interface SaveAttendanceResponse {
  success: boolean;
  message: string;
  results: {
    created: number;
    updated: number;
    errors: Array<{
      student_id: number;
      error: string;
    }>;
  };
  session_id: number;
  total_processed: number;
  timestamp: string;
}

export interface AttendanceStats {
  classroom_id: number;
  total_sessions: number;
  sessions_with_attendance: number;
  total_attendance_records: number;
  status_breakdown: {
    present?: number;
    absent?: number;
  };
  attendance_rate: number;
}

export const attendanceApi = {
  // Get attendance list for a session
  getSessionAttendance: async (sessionId: number): Promise<SessionAttendanceResponse> => {
    const response = await apiClient.get(`/sessions/${sessionId}/attendance`);
    return response.data.data;
  },

  // Save attendance for a session (bulk save)
  saveSessionAttendance: async (
    sessionId: number, 
    attendanceData: AttendanceRecord[]
  ): Promise<SaveAttendanceResponse> => {
    const response = await apiClient.post(`/sessions/${sessionId}/attendance`, {
      attendance_data: attendanceData
    });
    return response.data.data;
  },

  // Update attendance for a specific student
  updateStudentAttendance: async (
    sessionId: number,
    studentId: number,
    status: 'present' | 'absent',
    notes?: string
  ): Promise<SaveAttendanceResponse> => {
    const response = await apiClient.put(`/sessions/${sessionId}/attendance/${studentId}`, {
      status,
      notes
    });
    return response.data.data;
  },

  // Reset attendance for a session
  resetSessionAttendance: async (sessionId: number): Promise<any> => {
    const response = await apiClient.delete(`/sessions/${sessionId}/attendance`);
    return response.data.data;
  },

  // Get attendance statistics for a classroom
  getClassroomAttendanceStats: async (classroomId: number): Promise<AttendanceStats> => {
    const response = await apiClient.get(`/classrooms/${classroomId}/attendance/stats`);
    return response.data.data;
  },

  // Get attendance history for a student in a classroom
  getStudentAttendanceHistory: async (
    classroomId: number,
    studentId: number,
    page = 0,
    limit = 20
  ): Promise<any> => {
    const response = await apiClient.get(
      `/classrooms/${classroomId}/students/${studentId}/attendance?page=${page}&limit=${limit}`
    );
    return response.data.data;
  },

  // Export attendance report
  exportAttendanceReport: async (
    classroomId: number,
    format = 'json',
    startDate?: string,
    endDate?: string
  ): Promise<any> => {
    let url = `/classrooms/${classroomId}/attendance/export?format=${format}`;
    if (startDate) url += `&start_date=${startDate}`;
    if (endDate) url += `&end_date=${endDate}`;
    
    const response = await apiClient.get(url);
    return response.data.data;
  }
};
