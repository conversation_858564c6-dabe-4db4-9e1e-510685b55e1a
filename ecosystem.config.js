module.exports = {
  apps: [
    {
      name: 'cms-frontend',
      script: 'npm',
      args: 'start',
      cwd: './',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 9006,
        NEXT_PUBLIC_API_URL: 'https://vuquangduy.online/api'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 9006,
        NEXT_PUBLIC_API_URL: 'https://vuquangduy.online/api'
      },
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true
    }
  ]
};
