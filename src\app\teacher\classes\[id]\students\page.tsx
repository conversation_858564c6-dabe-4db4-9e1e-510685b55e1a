'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import { 
  ArrowLeft, 
  Users, 
  UserCheck, 
  UserX, 
  UserMinus,
  MoreHorizontal,
  Edit,
  Trash2
} from 'lucide-react';

import Button from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

import { classStudentsApi } from '@/lib/api/classStudents';
import { classroomsApi } from '@/lib/api/classrooms';
import { ClassStudent, Classroom, ClassStudentStats } from '@/types';

export default function ClassStudentsPage() {
  const params = useParams();
  const router = useRouter();
  const classroomId = parseInt(params.id as string);

  const [classroom, setClassroom] = useState<Classroom | null>(null);
  const [students, setStudents] = useState<ClassStudent[]>([]);
  const [stats, setStats] = useState<ClassStudentStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  // Dialog states
  const [showStatusDialog, setShowStatusDialog] = useState(false);
  const [showRemoveDialog, setShowRemoveDialog] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<ClassStudent | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<'active' | 'suspended' | 'withdrawn'>('active');
  const [notes, setNotes] = useState('');

  useEffect(() => {
    fetchData();
  }, [classroomId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [classroomData, studentsData, statsData] = await Promise.all([
        classroomsApi.getClassroomById(classroomId),
        classStudentsApi.getClassStudents(classroomId),
        classStudentsApi.getClassStudentStats(classroomId)
      ]);

      setClassroom(classroomData);
      setStudents(studentsData?.items || []);
      setStats(statsData);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Không thể tải dữ liệu');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = (student: ClassStudent, status: 'active' | 'suspended' | 'withdrawn') => {
    setSelectedStudent(student);
    setSelectedStatus(status);
    setNotes(student.teacher_notes || '');
    setShowStatusDialog(true);
  };

  const handleRemoveStudent = (student: ClassStudent) => {
    setSelectedStudent(student);
    setNotes('');
    setShowRemoveDialog(true);
  };

  const confirmStatusChange = async () => {
    if (!selectedStudent) return;

    console.log('🔍 Selected student:', selectedStudent);
    console.log('🔍 Student ID:', selectedStudent.student_id);

    try {
      setActionLoading(true);
      await classStudentsApi.updateStudentStatus(
        classroomId,
        selectedStudent.student_id,
        { status: selectedStatus, notes }
      );

      toast.success('Cập nhật trạng thái học sinh thành công');
      setShowStatusDialog(false);
      fetchData();
    } catch (error) {
      console.error('Error updating student status:', error);
      toast.error('Không thể cập nhật trạng thái học sinh');
    } finally {
      setActionLoading(false);
    }
  };

  const confirmRemoveStudent = async () => {
    if (!selectedStudent) return;

    console.log('🔍 Remove student:', selectedStudent);
    console.log('🔍 Student ID:', selectedStudent.student_id);

    try {
      setActionLoading(true);
      await classStudentsApi.removeStudentFromClass(
        classroomId,
        selectedStudent.student_id,
        notes
      );

      toast.success('Xóa học sinh khỏi lớp thành công');
      setShowRemoveDialog(false);
      fetchData();
    } catch (error) {
      console.error('Error removing student:', error);
      toast.error('Không thể xóa học sinh khỏi lớp');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Đang học</Badge>;
      case 'suspended':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Tạm hoãn</Badge>;
      case 'withdrawn':
        return <Badge variant="destructive">Đã rút</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href={`/teacher/classes/${classroomId}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Quản lý học sinh</h1>
            <p className="text-gray-600">{classroom?.name}</p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Tổng số học sinh</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalStudents}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <UserCheck className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Đang học</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.activeStudents}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <UserMinus className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Tạm hoãn</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.suspendedStudents}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <UserX className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Đã rút</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.withdrawnStudents}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Students List */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách học sinh</CardTitle>
        </CardHeader>
        <CardContent>
          {!students || students.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Chưa có học sinh nào trong lớp</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Học sinh
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Lớp
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Trạng thái
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ngày tham gia
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ghi chú
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Thao tác
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {students.map((student) => (
                    <tr key={student.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {student.student?.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {student.student?.email}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        Lớp {student.student?.grade}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(student.student_status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(student.joined_at).toLocaleDateString('vi-VN')}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                        {student.teacher_notes || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => handleStatusChange(student, 'active')}
                              disabled={student.student_status === 'active'}
                            >
                              <UserCheck className="mr-2 h-4 w-4" />
                              Kích hoạt
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleStatusChange(student, 'suspended')}
                              disabled={student.student_status === 'suspended'}
                            >
                              <UserMinus className="mr-2 h-4 w-4" />
                              Tạm hoãn
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleRemoveStudent(student)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Xóa khỏi lớp
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Status Change Dialog */}
      <Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cập nhật trạng thái học sinh</DialogTitle>
            <DialogDescription>
              Cập nhật trạng thái cho học sinh: {selectedStudent?.student?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="notes">Ghi chú</Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Nhập ghi chú về việc thay đổi trạng thái..."
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowStatusDialog(false)}>
              Hủy
            </Button>
            <Button onClick={confirmStatusChange} disabled={actionLoading}>
              {actionLoading ? 'Đang xử lý...' : 'Cập nhật'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Remove Student Dialog */}
      <Dialog open={showRemoveDialog} onOpenChange={setShowRemoveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xóa học sinh khỏi lớp</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa học sinh {selectedStudent?.student?.name} khỏi lớp?
              Hành động này sẽ đặt trạng thái học sinh thành "Đã rút".
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="remove-notes">Lý do xóa</Label>
              <Textarea
                id="remove-notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Nhập lý do xóa học sinh khỏi lớp..."
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRemoveDialog(false)}>
              Hủy
            </Button>
            <Button variant="destructive" onClick={confirmRemoveStudent} disabled={actionLoading}>
              {actionLoading ? 'Đang xử lý...' : 'Xóa khỏi lớp'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
