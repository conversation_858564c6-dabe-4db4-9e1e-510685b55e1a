'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { SubmissionsList } from '@/components/assignments';
import Link from 'next/link';
import Button from '@/components/ui/Button';
import { ArrowLeft } from 'lucide-react';

export default function AssignmentSubmissionsPage() {
  const params = useParams();
  const assignmentId = parseInt(params.id as string);
  const { user } = useAuth();

  // Redirect if not teacher
  if (user?.role !== 'teacher') {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600">Access denied. Only teachers can view this page.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href={`/teacher/assignments/${assignmentId}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Quay lại bài tập
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Danh sách bài nộp</h1>
          <p className="text-gray-600">Xem và chấm điểm các bài tập đã nộp</p>
        </div>
      </div>

      {/* Submissions List */}
      <SubmissionsList assignmentId={assignmentId} />
    </div>
  );
}
