'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { AssignmentList } from '@/components/assignments';
import { classroomsApi } from '@/lib/api/classrooms';
import Link from 'next/link';

export default function TeacherClassroomAssignmentsPage() {
  const params = useParams();
  const classroomId = parseInt(params.id as string);
  const { user, isTeacher } = useAuth();
  const [classroom, setClassroom] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isTeacher && classroomId) {
      fetchClassroom();
    }
  }, [isTeacher, classroomId]);

  const fetchClassroom = async () => {
    try {
      setLoading(true);
      const data = await classroomsApi.getClassroomById(classroomId);
      setClassroom(data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch classroom');
    } finally {
      setLoading(false);
    }
  };

  if (!isTeacher) {
    return (
      <div className="p-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">Access denied. Teachers only.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="p-4">
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error || !classroom) {
    return (
      <div className="p-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error || 'Classroom not found'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 to-teal-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{classroom.name}</h1>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-sm text-gray-600">{classroom.course_name}</span>
                  <span className="text-gray-400">•</span>
                  <span className="text-sm text-gray-600">{classroom.student_count} students</span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Link
                href={`/teacher/classes/${classroomId}/assignments/create`}
                className="inline-flex items-center px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700"
              >
                + Create Assignment
              </Link>
              <Link
                href={`/teacher/classes/${classroomId}`}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                ← Back to Class
              </Link>
            </div>
          </div>
        </div>

        {/* Assignments */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Assignment Management</h2>
            <p className="text-sm text-gray-600 mt-1">Create, manage and grade student assignments</p>
          </div>
          <div className="p-6">
            <AssignmentList classroomId={classroomId} userRole="teacher" />
          </div>
        </div>
      </div>
    </div>
  );
}
