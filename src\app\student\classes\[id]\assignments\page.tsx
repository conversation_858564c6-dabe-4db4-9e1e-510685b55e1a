'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { AssignmentList } from '@/components/assignments';
import { classroomsApi } from '@/lib/api/classrooms';
import Link from 'next/link';

export default function StudentClassroomAssignmentsPage() {
  const params = useParams();
  const classroomId = parseInt(params.id as string);
  const { user, isStudent } = useAuth();
  const [classroom, setClassroom] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isStudent && classroomId) {
      fetchClassroom();
    }
  }, [isStudent, classroomId]);

  const fetchClassroom = async () => {
    try {
      setLoading(true);
      const data = await classroomsApi.getClassroomForStudent(classroomId);
      setClassroom(data);
      setError(null);
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to fetch classroom';
      setError(errorMessage);

      // If access denied, redirect back to classes list
      if (err.response?.status === 403) {
        setTimeout(() => {
          window.location.href = '/student/my-classes';
        }, 3000);
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isStudent) {
    return (
      <div className="p-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">Access denied. Students only.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="p-4">
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error || !classroom) {
    return (
      <div className="p-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error || 'Classroom not found'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{classroom.name}</h1>
                <p className="text-gray-600">
                  {classroom.course?.name || classroom.course_name || 'Course'}
                </p>
              </div>
            </div>
            <Link
              href={`/student/classes/${classroomId}`}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              ← Back to Class
            </Link>
          </div>
        </div>

        {/* Assignments */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">My Assignments</h2>
          </div>
          <div className="p-6">
            <AssignmentList classroomId={classroomId} userRole="student" />
          </div>
        </div>
      </div>
    </div>
  );
}
