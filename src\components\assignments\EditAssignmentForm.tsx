'use client';

import React, { useState, useEffect } from 'react';
import { Assignment, UpdateAssignmentData, assignmentsApi } from '@/lib/api/assignments';
import { useRouter } from 'next/navigation';

interface EditAssignmentFormProps {
  assignmentId: number;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export default function EditAssignmentForm({ assignmentId, onSuccess, onCancel }: EditAssignmentFormProps) {
  const [assignment, setAssignment] = useState<Assignment | null>(null);
  const [formData, setFormData] = useState<UpdateAssignmentData>({
    title: '',
    description: '',
    type: 'homework',
    deadline: '',
    max_score: 100,
    instructions: '',
    is_active: true
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();

  useEffect(() => {
    fetchAssignment();
  }, [assignmentId]);

  const fetchAssignment = async () => {
    try {
      setLoading(true);
      const data = await assignmentsApi.getAssignment(assignmentId);
      setAssignment(data);
      
      // Populate form with existing data
      setFormData({
        title: data.title,
        description: data.description || '',
        type: data.type,
        deadline: new Date(data.deadline).toISOString().slice(0, 16),
        max_score: data.max_score,
        instructions: data.instructions || '',
        is_active: data.is_active
      });
      
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch assignment');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title?.trim()) {
      setError('Title is required');
      return;
    }
    
    if (!formData.deadline) {
      setError('Deadline is required');
      return;
    }
    
    // Check if deadline is in the future (only if changed)
    const deadlineDate = new Date(formData.deadline);
    const originalDeadline = new Date(assignment?.deadline || '');
    const now = new Date();
    
    if (deadlineDate.getTime() !== originalDeadline.getTime() && deadlineDate <= now) {
      setError('New deadline must be in the future');
      return;
    }

    try {
      setSaving(true);
      setError(null);
      
      const updatedAssignment = await assignmentsApi.updateAssignment(assignmentId, formData);
      
      if (onSuccess) {
        onSuccess();
      } else {
        router.push(`/teacher/assignments/${updatedAssignment.id}`);
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update assignment');
    } finally {
      setSaving(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : name === 'max_score' ? parseFloat(value) || 0 
              : value
    }));
  };

  // Get minimum datetime for deadline (current time + 1 hour)
  const getMinDeadline = () => {
    const now = new Date();
    now.setHours(now.getHours() + 1);
    return now.toISOString().slice(0, 16);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error && !assignment) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600">{error}</p>
        <button 
          onClick={fetchAssignment}
          className="mt-2 text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Edit Assignment</h2>
        <p className="text-gray-600 mt-1">Update assignment details and settings</p>
      </div>

      {error && (
        <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-3">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Title */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
            Title *
          </label>
          <input
            type="text"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter assignment title"
          />
        </div>

        {/* Type */}
        <div>
          <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
            Type
          </label>
          <select
            id="type"
            name="type"
            value={formData.type}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="homework">📝 Homework</option>
            <option value="exam">📋 Exam</option>
            <option value="quiz">❓ Quiz</option>
          </select>
        </div>

        {/* Deadline */}
        <div>
          <label htmlFor="deadline" className="block text-sm font-medium text-gray-700 mb-1">
            Deadline *
          </label>
          <input
            type="datetime-local"
            id="deadline"
            name="deadline"
            value={formData.deadline}
            onChange={handleChange}
            min={getMinDeadline()}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <p className="text-xs text-gray-500 mt-1">
            Original deadline: {assignment ? new Date(assignment.deadline).toLocaleString() : 'N/A'}
          </p>
        </div>

        {/* Max Score */}
        <div>
          <label htmlFor="max_score" className="block text-sm font-medium text-gray-700 mb-1">
            Maximum Score
          </label>
          <input
            type="number"
            id="max_score"
            name="max_score"
            value={formData.max_score}
            onChange={handleChange}
            min="0"
            max="1000"
            step="0.01"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter assignment description"
          />
        </div>

        {/* Instructions */}
        <div>
          <label htmlFor="instructions" className="block text-sm font-medium text-gray-700 mb-1">
            Instructions
          </label>
          <textarea
            id="instructions"
            name="instructions"
            value={formData.instructions}
            onChange={handleChange}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter detailed instructions for students"
          />
        </div>

        {/* Active Status */}
        <div className="flex items-center">
          <input
            type="checkbox"
            id="is_active"
            name="is_active"
            checked={formData.is_active}
            onChange={handleChange}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="is_active" className="ml-2 block text-sm text-gray-700">
            Assignment is active (students can see and submit)
          </label>
        </div>

        {/* Buttons */}
        <div className="flex justify-end gap-3 pt-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={saving}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {saving ? 'Saving...' : 'Update Assignment'}
          </button>
        </div>
      </form>
    </div>
  );
}
