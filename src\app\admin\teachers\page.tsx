'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { TeacherWithStats, PaginatedResponse } from '@/types';
import { formatDateTime, getStatusColor } from '@/lib/utils';
import { teachersApi } from '@/lib/api/teachers';
import Button from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { ArrowLeft, Plus, Search, RefreshCw, UserCheck, UserX, Edit, Trash2 } from 'lucide-react';
import toast from 'react-hot-toast';

export default function TeachersPage() {
  const router = useRouter();
  const { isAdmin } = useAuth();
  
  const [teachers, setTeachers] = useState<PaginatedResponse<TeacherWithStats> | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize] = useState(10);

  // Redirect if not admin
  useEffect(() => {
    if (!isAdmin) {
      router.push('/dashboard');
    }
  }, [isAdmin, router]);

  // Fetch teachers on mount and when page/search changes
  useEffect(() => {
    if (isAdmin) {
      fetchTeachers();
    }
  }, [isAdmin, currentPage, searchTerm]);

  const fetchTeachers = async () => {
    try {
      setLoading(true);
      const data = await teachersApi.getTeachers({
        page: currentPage,
        limit: pageSize,
        search: searchTerm || undefined
      });
      setTeachers(data);
    } catch (error) {
      console.error('Error fetching teachers:', error);
      toast.error('Không thể tải danh sách giáo viên');
    } finally {
      setLoading(false);
    }
  };

  const toggleTeacherStatus = async (teacherId: number, currentStatus: boolean) => {
    try {
      await teachersApi.toggleTeacherStatus(teacherId, !currentStatus);
      toast.success(`${!currentStatus ? 'Kích hoạt' : 'Vô hiệu hóa'} giáo viên thành công`);
      fetchTeachers(); // Refresh list
    } catch (error) {
      console.error('Error toggling teacher status:', error);
      toast.error('Không thể cập nhật trạng thái giáo viên');
    }
  };

  const deleteTeacher = async (teacherId: number, teacherName: string) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa giáo viên "${teacherName}"?\n\nHành động này sẽ vô hiệu hóa tài khoản và không thể hoàn tác.`)) {
      return;
    }

    try {
      await teachersApi.deleteTeacher(teacherId);
      toast.success('Xóa giáo viên thành công');
      fetchTeachers(); // Refresh list
    } catch (error) {
      console.error('Error deleting teacher:', error);
      toast.error('Không thể xóa giáo viên');
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(0); // Reset to first page on new search
    fetchTeachers();
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  if (!isAdmin) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div className="flex items-center space-x-2">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Quay lại
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Quản lý giáo viên</h1>
              <p className="text-gray-600">Quản lý tài khoản giáo viên trong hệ thống</p>
            </div>
          </div>
          <Link href="/admin/teachers/create">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Thêm giáo viên
            </Button>
          </Link>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-4">
            <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-2">
              <div className="relative flex-grow">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Tìm kiếm theo tên hoặc email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <Button type="submit" variant="outline">
                <Search className="mr-2 h-4 w-4" />
                Tìm kiếm
              </Button>
              <Button type="button" variant="outline" onClick={() => fetchTeachers()}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Làm mới
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Teachers List */}
        <Card>
          <CardHeader>
            <CardTitle>Danh sách giáo viên</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-500">Đang tải...</p>
              </div>
            ) : teachers && teachers.items && teachers.items.length > 0 ? (
              <div className="space-y-4">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Giáo viên
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Lớp học
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Ngày tham gia
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Trạng thái
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Thao tác
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {teachers.items.map((teacher) => (
                        <tr key={teacher.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                <span className="text-sm font-medium text-gray-700">
                                  {teacher.name.charAt(0).toUpperCase()}
                                </span>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">{teacher.name}</div>
                                <div className="text-sm text-gray-500">{teacher.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{teacher.total_classes || 0} lớp</div>
                            <div className="text-sm text-gray-500">{teacher.active_classes || 0} lớp đang hoạt động</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{formatDateTime(teacher.created_at)}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(teacher.is_active ? 'active' : 'inactive')}`}>
                              {teacher.is_active ? 'Hoạt động' : 'Vô hiệu hóa'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex items-center justify-end space-x-2">
                              <Link href={`/admin/teachers/${teacher.id}/edit`}>
                                <Button variant="outline" size="sm">
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </Link>

                              <Button
                                variant={teacher.is_active ? 'danger' : 'primary'}
                                size="sm"
                                onClick={() => toggleTeacherStatus(teacher.id, teacher.is_active)}
                              >
                                {teacher.is_active ? (
                                  <>
                                    <UserX className="mr-1 h-4 w-4" />
                                    Vô hiệu hóa
                                  </>
                                ) : (
                                  <>
                                    <UserCheck className="mr-1 h-4 w-4" />
                                    Kích hoạt
                                  </>
                                )}
                              </Button>

                              <Button
                                variant="danger"
                                size="sm"
                                onClick={() => deleteTeacher(teacher.id, teacher.name)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {teachers.totalPages > 1 && (
                  <div className="flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6">
                    <div className="flex flex-1 justify-between sm:hidden">
                      <Button
                        variant="outline"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 0}
                      >
                        Trước
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage >= teachers.totalPages - 1}
                      >
                        Sau
                      </Button>
                    </div>
                    <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                      <div>
                        <p className="text-sm text-gray-700">
                          Hiển thị <span className="font-medium">{currentPage * pageSize + 1}</span> đến{' '}
                          <span className="font-medium">
                            {Math.min((currentPage + 1) * pageSize, teachers.total)}
                          </span>{' '}
                          trong tổng số <span className="font-medium">{teachers.total}</span> giáo viên
                        </p>
                      </div>
                      <div>
                        <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                          <Button
                            variant="outline"
                            className="rounded-l-md"
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 0}
                          >
                            Trước
                          </Button>
                          {Array.from({ length: teachers.totalPages }, (_, i) => (
                            <Button
                              key={i}
                              variant={i === currentPage ? 'primary' : 'outline'}
                              onClick={() => handlePageChange(i)}
                              className="rounded-none"
                            >
                              {i + 1}
                            </Button>
                          ))}
                          <Button
                            variant="outline"
                            className="rounded-r-md"
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage >= teachers.totalPages - 1}
                          >
                            Sau
                          </Button>
                        </nav>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <UserX className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Không tìm thấy giáo viên</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm ? 'Không có kết quả phù hợp với tìm kiếm của bạn.' : 'Chưa có giáo viên nào trong hệ thống.'}
                </p>
                <div className="mt-6">
                  <Link href="/admin/teachers/create">
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Thêm giáo viên
                    </Button>
                  </Link>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
