'use client';

import React, { useState, useEffect } from 'react';
import { AssignmentSubmission, assignmentsApi } from '@/lib/api/assignments';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import FilePreview from './FilePreview';
import {
  Users,
  CheckCircle,
  Clock,
  AlertCircle,
  Star,
  FileText,
  Calendar,
  User,
  GraduationCap,
  Filter,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

interface SubmissionsListProps {
  assignmentId: number;
}

export default function SubmissionsList({ assignmentId }: SubmissionsListProps) {
  const [submissions, setSubmissions] = useState<AssignmentSubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [gradingSubmission, setGradingSubmission] = useState<number | null>(null);
  const [gradeForm, setGradeForm] = useState({ score: '', feedback: '' });

  useEffect(() => {
    fetchSubmissions();
  }, [assignmentId, currentPage, statusFilter]);

  const fetchSubmissions = async () => {
    try {
      setLoading(true);
      const response = await assignmentsApi.getAssignmentSubmissions(
        assignmentId, 
        currentPage, 
        10, 
        statusFilter || undefined
      );
      setSubmissions(response.data);
      setTotalPages(response.pagination.totalPages);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch submissions');
    } finally {
      setLoading(false);
    }
  };

  const handleGradeSubmission = async (submissionId: number) => {
    try {
      const score = gradeForm.score ? parseFloat(gradeForm.score) : undefined;
      await assignmentsApi.gradeSubmission(submissionId, {
        score,
        feedback: gradeForm.feedback || undefined
      });
      
      setGradingSubmission(null);
      setGradeForm({ score: '', feedback: '' });
      fetchSubmissions(); // Refresh list
    } catch (err: any) {
      alert(err.response?.data?.message || 'Failed to grade submission');
    }
  };

  const startGrading = (submission: AssignmentSubmission) => {
    setGradingSubmission(submission.id);
    setGradeForm({
      score: (submission.score !== null && submission.score !== undefined) ? submission.score.toString() : '',
      feedback: submission.feedback || ''
    });
  };

  const cancelGrading = () => {
    setGradingSubmission(null);
    setGradeForm({ score: '', feedback: '' });
  };

  const getStatusBadge = (status: string, isLate: boolean) => {
    let bgColor = 'bg-gray-100';
    let textColor = 'text-gray-600';
    
    switch (status) {
      case 'submitted':
        bgColor = 'bg-blue-100';
        textColor = 'text-blue-600';
        break;
      case 'graded':
        bgColor = 'bg-green-100';
        textColor = 'text-green-600';
        break;
      case 'returned':
        bgColor = 'bg-purple-100';
        textColor = 'text-purple-600';
        break;
    }

    return (
      <div className="flex items-center gap-2">
        <span className={`px-2 py-1 text-xs rounded-full ${bgColor} ${textColor}`}>
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </span>
        {isLate && (
          <span className="px-2 py-1 text-xs bg-red-100 text-red-600 rounded-full">
            Late
          </span>
        )}
      </div>
    );
  };

  const formatFileSize = (bytes: number) => {
    return assignmentsApi.formatFileSize(bytes);
  };

  const getFileIcon = (mimetype: string) => {
    return assignmentsApi.getFileTypeIcon(mimetype);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600">{error}</p>
        <button 
          onClick={fetchSubmissions}
          className="mt-2 text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Users className="mr-2 h-5 w-5" />
              Danh sách bài nộp
            </CardTitle>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <FileText className="h-4 w-4" />
                <span>Tổng: {submissions.length} bài nộp</span>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filter */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <select
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setCurrentPage(0);
                }}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value="">Tất cả trạng thái</option>
                <option value="submitted">Đã nộp</option>
                <option value="graded">Đã chấm</option>
                <option value="returned">Đã trả</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submissions List */}
      {submissions.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">Chưa có bài nộp nào</h3>
            <p className="mt-2 text-gray-600">
              Các bài nộp của học sinh sẽ hiển thị ở đây khi họ submit bài tập.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {submissions.map((submission) => (
            <Card key={submission.id}>
              <CardContent className="p-6">
                {/* Student Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <User className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{submission.student_name}</h3>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <span>{submission.student_email}</span>
                        <span>•</span>
                        <div className="flex items-center gap-1">
                          <GraduationCap className="h-3 w-3" />
                          <span>Lớp {submission.student_grade}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    {getStatusBadge(submission.status, submission.is_late)}
                  {submission.score !== null && submission.score !== undefined && (
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <div className="text-right">
                        <p className="font-bold text-lg text-gray-900">
                          {submission.score}/{submission.max_score || 100}
                        </p>
                        <p className="text-sm text-gray-500">
                          {(((submission.score || 0) / (submission.max_score || 100)) * 100).toFixed(1)}%
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

                {/* Submission Time */}
                <div className="flex items-center gap-2 text-sm text-gray-600 mb-4">
                  <Calendar className="h-4 w-4" />
                  <span>Nộp lúc: {new Date(submission.submitted_at).toLocaleString()}</span>
                  {submission.is_late && (
                    <span className="ml-2 px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full">
                      Nộp muộn
                    </span>
                  )}
                </div>

                {/* Submitted Files */}
                {submission.submission_files && submission.submission_files.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      File đã nộp ({submission.submission_files.length})
                    </h4>
                    <div className="space-y-2">
                      {submission.submission_files.map((file, index) => (
                        <FilePreview key={index} file={file} showPreview={true} />
                      ))}
                    </div>
                  </div>
                )}

              {/* Feedback */}
              {submission.feedback && (
                <div className="mb-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Nhận xét của giáo viên
                  </h4>
                  <p className="text-blue-800 text-sm whitespace-pre-wrap leading-relaxed">{submission.feedback}</p>
                </div>
              )}

              {/* Grading Section */}
              {gradingSubmission === submission.id ? (
                <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Star className="h-5 w-5 text-yellow-500" />
                    Chấm điểm bài tập
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Score (out of {submission.max_score})
                      </label>
                      <input
                        type="number"
                        min="0"
                        max={submission.max_score}
                        step="0.01"
                        value={gradeForm.score}
                        onChange={(e) => setGradeForm(prev => ({ ...prev, score: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter score"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Feedback
                      </label>
                      <textarea
                        value={gradeForm.feedback}
                        onChange={(e) => setGradeForm(prev => ({ ...prev, feedback: e.target.value }))}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter feedback for student"
                      />
                    </div>
                  </div>
                  <div className="flex justify-end gap-2 mt-4">
                    <button
                      onClick={cancelGrading}
                      className="px-4 py-2 text-gray-700 bg-gray-100 rounded hover:bg-gray-200 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={() => handleGradeSubmission(submission.id)}
                      className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                    >
                      Save Grade
                    </button>
                  </div>
                </div>
              ) : (
                <div className="flex justify-end">
                  <button
                    onClick={() => startGrading(submission)}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  >
                    {(submission.score !== null && submission.score !== undefined) ? 'Cập nhật điểm' : 'Chấm điểm'}
                  </button>
                </div>
              )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-2 mt-6">
          <button
            onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
            disabled={currentPage === 0}
            className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Previous
          </button>
          
          <span className="text-sm text-gray-600">
            Page {currentPage + 1} of {totalPages}
          </span>
          
          <button
            onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
            disabled={currentPage >= totalPages - 1}
            className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
}
