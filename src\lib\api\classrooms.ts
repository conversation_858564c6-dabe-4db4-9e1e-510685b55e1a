import { apiClient } from './client';
import { Classroom, CreateClassroomRequest, UpdateClassroomRequest, PaginatedResponse } from '@/types';

export const classroomsApi = {
  // Get all classrooms for teacher
  getClassrooms: async (page = 0, limit = 10, search?: string): Promise<PaginatedResponse<Classroom>> => {
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    if (search) {
      params.append('search', search);
    }

    const response = await apiClient.get(`/classrooms?${params.toString()}`);
    return response.data.data; // Backend returns { success: true, data: {...} }
  },

  // Get classroom by ID
  getClassroomById: async (id: number): Promise<Classroom> => {
    const response = await apiClient.get(`/classrooms/${id}`);
    return response.data.data;
  },

  // Create new classroom
  createClassroom: async (data: CreateClassroomRequest): Promise<Classroom> => {
    const response = await apiClient.post('/classrooms', data);
    return response.data.data;
  },

  // Update classroom
  updateClassroom: async (id: number, data: UpdateClassroomRequest): Promise<Classroom> => {
    const response = await apiClient.put(`/classrooms/${id}`, data);
    return response.data.data;
  },

  // Delete classroom
  deleteClassroom: async (id: number): Promise<{ message: string }> => {
    const response = await apiClient.delete(`/classrooms/${id}`);
    return response.data;
  },

  // Get classroom statistics
  getClassroomStats: async () => {
    const response = await apiClient.get('/classrooms/stats');
    return response.data.data;
  },

  // Alias for getClassroomById for backward compatibility
  getClassroom: async (id: number): Promise<Classroom> => {
    return classroomsApi.getClassroomById(id);
  },

  // Get classroom details for student (enrolled students only)
  getClassroomForStudent: async (id: number): Promise<Classroom> => {
    const response = await apiClient.get(`/classrooms/${id}/student-view`);
    return response.data.data;
  },
};
