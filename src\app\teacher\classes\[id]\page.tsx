'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
// import DashboardLayout from '@/components/layout/DashboardLayout';
import { Classroom } from '@/types';
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils';
import { classroomsApi } from '@/lib/api/classrooms';
import Button from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { ArrowLeft, Edit, GraduationCap, BookOpen, Calendar, DollarSign, Users, Clock } from 'lucide-react';
import toast from 'react-hot-toast';

export default function ClassroomDetailPage() {
  const router = useRouter();
  const params = useParams();
  const { isTeacher } = useAuth();
  const classroomId = parseInt(params.id as string);
  
  const [classroom, setClassroom] = useState<Classroom | null>(null);
  const [loading, setLoading] = useState(true);

  // Redirect if not teacher
  useEffect(() => {
    if (!isTeacher) {
      router.push('/dashboard');
    }
  }, [isTeacher, router]);

  // Fetch classroom data
  useEffect(() => {
    if (isTeacher && classroomId) {
      fetchClassroom();
    }
  }, [isTeacher, classroomId]);

  const fetchClassroom = async () => {
    try {
      setLoading(true);
      const data = await classroomsApi.getClassroomById(classroomId);
      console.log("data chi tiet cua lop hoc  " , data);
      setClassroom(data);
    } catch (error) {
      console.error('Error fetching classroom:', error);
      toast.error('Không thể tải thông tin lớp học');
      router.push('/teacher/classes');
    } finally {
      setLoading(false);
    }
  };

  if (!isTeacher) {
    return null;
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Đang tải thông tin lớp học...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div className="flex items-center space-x-2">
            <Link href="/teacher/classes">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Quay lại
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{classroom?.name}</h1>
              <p className="text-gray-600">Chi tiết lớp học và quản lý học sinh</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Link href={`/teacher/classes/${classroomId}/students`}>
              <Button variant="outline">
                <Users className="mr-2 h-4 w-4" />
                Quản lý học sinh
              </Button>
            </Link>
            <Link href={`/teacher/classes/${classroomId}/sessions`}>
              <Button variant="outline">
                <Calendar className="mr-2 h-4 w-4" />
                Quản lý lịch học
              </Button>
            </Link>
            <Link href={`/teacher/classes/${classroomId}/assignments`}>
              <Button variant="outline">
                📝 Bài tập
              </Button>
            </Link>
            <Link href={`/teacher/classes/${classroomId}/edit`}>
              <Button variant="outline">
                <Edit className="mr-2 h-4 w-4" />
                Chỉnh sửa
              </Button>
            </Link>
          </div>
        </div>

        {/* Classroom Info */}
        {classroom && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Classroom Details */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <div className="flex items-center">
                    <GraduationCap className="mr-2 h-5 w-5" />
                    <CardTitle>Thông tin lớp học</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{classroom.name}</h3>
                    <p className="text-gray-600 mt-1">{classroom.description || 'Không có mô tả'}</p>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Khóa học</p>
                      <p className="text-sm text-gray-900">{classroom.course?.name || 'N/A'}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Học phí mỗi buổi</p>
                      <p className="text-sm text-gray-900">{formatCurrency(classroom.fee_per_session)}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Sĩ số tối đa</p>
                      <p className="text-sm text-gray-900">{classroom.max_students} học sinh</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Trạng thái</p>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(classroom.is_active ? 'active' : 'inactive')}`}>
                        {classroom.is_active ? 'Đang hoạt động' : 'Không hoạt động'}
                      </span>
                    </div>
                    {classroom.start_date && (
                      <div>
                        <p className="text-sm font-medium text-gray-500">Ngày bắt đầu</p>
                        <p className="text-sm text-gray-900">{formatDate(classroom.start_date)}</p>
                      </div>
                    )}
                    {classroom.end_date && (
                      <div>
                        <p className="text-sm font-medium text-gray-500">Ngày kết thúc</p>
                        <p className="text-sm text-gray-900">{formatDate(classroom.end_date)}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Classroom Stats */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Thống kê lớp học</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-blue-600 mr-2" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{classroom.enrolled_count || 0}/{classroom.max_students}</p>
                      <p className="text-xs text-gray-500">Học sinh đã đăng ký</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-green-600 mr-2" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">0 buổi học</p>
                      <p className="text-xs text-gray-500">Đã tổ chức</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-orange-600 mr-2" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">0%</p>
                      <p className="text-xs text-gray-500">Tỷ lệ tham gia TB</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <DollarSign className="h-5 w-5 text-purple-600 mr-2" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">0 ₫</p>
                      <p className="text-xs text-gray-500">Tổng thu nhập</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <Users className="h-8 w-8 text-blue-600 mx-auto" />
                <h3 className="mt-2 text-lg font-medium text-gray-900">Học sinh</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Quản lý đăng ký và học sinh
                </p>
                <div className="mt-4">
                  <Link href={`/teacher/classes/${classroomId}/students`}>
                    <Button variant="outline" className="w-full">
                      Xem danh sách
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <Calendar className="h-8 w-8 text-green-600 mx-auto" />
                <h3 className="mt-2 text-lg font-medium text-gray-900">Lịch học</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Tạo và quản lý buổi học
                </p>
                <div className="mt-4">
                  <Link href={`/teacher/classes/${classroomId}/sessions`}>
                    <Button variant="outline" className="w-full">
                      Xem lịch học
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <Clock className="h-8 w-8 text-orange-600 mx-auto" />
                <h3 className="mt-2 text-lg font-medium text-gray-900">Điểm danh</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Điểm danh và theo dõi tham gia
                </p>
                <div className="mt-4">
                  <Button variant="outline" className="w-full">
                    Điểm danh
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <DollarSign className="h-8 w-8 text-purple-600 mx-auto" />
                <h3 className="mt-2 text-lg font-medium text-gray-900">Học phí</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Tính toán và xuất báo cáo
                </p>
                <div className="mt-4">
                  <Button variant="outline" className="w-full">
                    Tính học phí
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Hoạt động gần đây</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Chưa có hoạt động</h3>
              <p className="mt-1 text-sm text-gray-500">
                Các hoạt động của lớp học sẽ hiển thị ở đây.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    
  );
}
