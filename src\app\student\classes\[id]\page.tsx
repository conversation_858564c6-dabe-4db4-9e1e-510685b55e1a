'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { classroomsApi } from '@/lib/api/classrooms';
import Button from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { ArrowLeft, GraduationCap, Calendar, Users, Clock } from 'lucide-react';

export default function StudentClassroomDetailPage() {
  const params = useParams();
  const classroomId = parseInt(params.id as string);
  const { user } = useAuth();
  const [classroom, setClassroom] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchClassroom();
  }, [classroomId]);

  const fetchClassroom = async () => {
    try {
      setLoading(true);
      const data = await classroomsApi.getClassroomForStudent(classroomId);
      setClassroom(data);
      setError(null);
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to fetch classroom';
      setError(errorMessage);

      // If access denied, redirect back to classes list
      if (err.response?.status === 403) {
        setTimeout(() => {
          window.location.href = '/student/my-classes';
        }, 3000);
      }
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !classroom) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link href="/student/my-classes">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Quay lại
            </Button>
          </Link>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <GraduationCap className="h-8 w-8 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-red-800">Không thể truy cập lớp học</h3>
              <p className="text-red-600 mt-1">{error || 'Classroom not found'}</p>
              {error && error.includes('pending') && (
                <p className="text-sm text-red-500 mt-2">
                  Vui lòng chờ giáo viên duyệt đăng ký của bạn.
                </p>
              )}
              {error && error.includes('not enrolled') && (
                <p className="text-sm text-red-500 mt-2">
                  Bạn cần đăng ký lớp học này trước khi có thể xem chi tiết.
                </p>
              )}
            </div>
          </div>
          <div className="mt-4 flex gap-3">
            <button
              onClick={fetchClassroom}
              className="text-red-600 hover:text-red-800 underline text-sm"
            >
              Thử lại
            </button>
            <Link
              href="/student/classes"
              className="text-red-600 hover:text-red-800 underline text-sm"
            >
              Tìm lớp học khác
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/student/classes">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{classroom?.name}</h1>
              <p className="text-gray-600">Class Details and Resources</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Link href={`/student/classes/${classroomId}/assignments`}>
              <Button variant="outline">
                📝 Assignments
              </Button>
            </Link>
          </div>
        </div>

        {/* Classroom Info */}
        {classroom && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
            {/* Classroom Details */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <div className="flex items-center">
                    <GraduationCap className="mr-2 h-5 w-5" />
                    <CardTitle>Class Information</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Course</p>
                      <p className="text-gray-900">{classroom.course_name}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Teacher</p>
                      <p className="text-gray-900">{classroom.teacher_name}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Schedule</p>
                      <p className="text-gray-900">{classroom.schedule || 'Not specified'}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Students</p>
                      <p className="text-gray-900">{classroom.student_count} students</p>
                    </div>
                  </div>
                  
                  {classroom.description && (
                    <div>
                      <p className="text-sm font-medium text-gray-500 mb-2">Description</p>
                      <p className="text-gray-700">{classroom.description}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Quick Stats */}
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Link href={`/student/classes/${classroomId}/assignments`}>
                    <Button variant="outline" className="w-full justify-start">
                      📝 View Assignments
                    </Button>
                  </Link>
                  <Button variant="outline" className="w-full justify-start" disabled>
                    📅 Class Schedule
                  </Button>
                  <Button variant="outline" className="w-full justify-start" disabled>
                    📊 My Grades
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Class Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Status</span>
                      <span className={`text-sm px-2 py-1 rounded-full ${
                        classroom.status === 'active' 
                          ? 'bg-green-100 text-green-600' 
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        {classroom.status === 'active' ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Enrollment</span>
                      <span className="text-sm text-green-600">Approved</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h2>
        <div className="space-y-3">
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              📝
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">New assignment available</p>
              <p className="text-xs text-gray-500">Check the assignments section for new work</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              ✅
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">Enrolled in class</p>
              <p className="text-xs text-gray-500">You have been approved for this class</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
