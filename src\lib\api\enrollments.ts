import { apiClient } from './client';
import { Enrollment, PaginatedResponse } from '@/types';

export const enrollmentsApi = {
  // Get all enrollments for teacher
  getEnrollments: async (page = 0, limit = 10): Promise<PaginatedResponse<Enrollment>> => {
    const response = await apiClient.get(`/enrollments/teacher?page=${page}&limit=${limit}`);
    return response.data.data;
  },

  // Get enrollment by ID
  getEnrollmentById: async (id: number): Promise<Enrollment> => {
    const response = await apiClient.get(`/enrollments/${id}`);
    return response.data.data;
  },

  // Update enrollment status
  updateEnrollmentStatus: async (id: number, status: 'approved' | 'rejected', notes?: string) => {
    const response = await apiClient.put(`/enrollments/${id}/status`, { status, notes });
    return response.data.data;
  },

  // Get enrollment statistics
  getEnrollmentStats: async () => {
    const response = await apiClient.get('/enrollments/stats');
    return response.data.data;
  },

  // Get student's own enrollments
  getStudentEnrollments: async (page = 0, limit = 10): Promise<PaginatedResponse<Enrollment>> => {
    const response = await apiClient.get(`/enrollments/my?page=${page}&limit=${limit}`);
    return response.data.data;
  },

  // Get available classrooms for student
  getAvailableClassrooms: async (page = 0, limit = 10, search?: string): Promise<PaginatedResponse<any>> => {
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    if (search) {
      params.append('search', search);
    }

    const response = await apiClient.get(`/enrollments/available?${params.toString()}`);
    return response.data.data;
  },

  // Student enrolls in classroom
  enrollInClassroom: async (classroomId: number) => {
    const response = await apiClient.post('/enrollments', { classroom_id: classroomId });
    return response.data.data;
  },

  // Get student's own enrollments
  getMyEnrollments: async () => {
    console.log('🔍 API: Calling /enrollments/my');
    const response = await apiClient.get('/enrollments/my?limit=100'); // Get all enrollments
    console.log('🔍 API: Raw response:', response);
    console.log('🔍 API: Response data:', response.data);
    console.log('🔍 API: Response data.data:', response.data.data);
    console.log('🔍 API: Response data.data.items:', response.data.data?.items);
    return response.data.data.items; // Return items array
  },

  // Cancel enrollment (Student only)
  cancelEnrollment: async (enrollmentId: number) => {
    const response = await apiClient.delete(`/enrollments/${enrollmentId}`);
    return response.data.data;
  },
};
