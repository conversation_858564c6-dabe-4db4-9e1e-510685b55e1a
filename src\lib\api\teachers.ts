import { apiClient } from './client';
import { TeacherWithStats, PaginatedResponse } from '@/types';

export const teachersApi = {
  // Get all teachers
  getTeachers: async (params: {
    page?: number;
    limit?: number;
    search?: string;
  } = {}): Promise<PaginatedResponse<TeacherWithStats>> => {
    const { page = 0, limit = 10, search } = params;
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());
    if (search) {
      queryParams.append('search', search);
    }

    const response = await apiClient.get(`/admin/teachers?${queryParams.toString()}`);
    return response.data.data;
  },

  // Get teacher by ID
  getTeacherById: async (id: number): Promise<TeacherWithStats> => {
    const response = await apiClient.get(`/admin/teachers/${id}`);
    return response.data.data;
  },

  // Update teacher
  updateTeacher: async (id: number, data: {
    name: string;
    email: string;
    is_active: boolean;
  }): Promise<TeacherWithStats> => {
    const response = await apiClient.put(`/admin/teachers/${id}`, data);
    return response.data.data;
  },

  // Toggle teacher status
  toggleTeacherStatus: async (id: number, is_active: boolean): Promise<TeacherWithStats> => {
    const response = await apiClient.put(`/admin/teachers/${id}/status`, { is_active });
    return response.data.data;
  },

  // Create teacher
  createTeacher: async (data: {
    name: string;
    email: string;
    password: string;
  }): Promise<TeacherWithStats> => {
    const response = await apiClient.post('/admin/teachers', data);
    return response.data.data;
  },

  // Delete teacher
  deleteTeacher: async (id: number): Promise<{ message: string }> => {
    const response = await apiClient.delete(`/admin/teachers/${id}`);
    return response.data;
  },
};
