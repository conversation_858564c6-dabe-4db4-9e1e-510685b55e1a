'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Classroom, Course } from '@/types';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/Card';
import { classroomsApi } from '@/lib/api/classrooms';
import { coursesApi } from '@/lib/api/courses';
import toast from 'react-hot-toast';
import { ArrowLeft, Save, GraduationCap } from 'lucide-react';
import Link from 'next/link';

export default function EditClassroomPage() {
  const router = useRouter();
  const params = useParams();
  const { isTeacher } = useAuth();
  const classroomId = parseInt(params.id as string);
  
  const [classroom, setClassroom] = useState<Classroom | null>(null);
  const [courses, setCourses] = useState<Course[]>([]);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    course_id: 0,
    grade: 1,
    start_date: '',
    end_date: '',
    fee_per_session: 0,
    max_students: 20,
    is_active: true,
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [coursesLoading, setCoursesLoading] = useState(true);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  // Redirect if not teacher
  useEffect(() => {
    if (!isTeacher) {
      router.push('/dashboard');
    }
  }, [isTeacher, router]);

  // Fetch classroom and courses data
  useEffect(() => {
    if (isTeacher && classroomId) {
      fetchClassroom();
      fetchActiveCourses();
    }
  }, [isTeacher, classroomId]);

  const fetchClassroom = async () => {
    try {
      setLoading(true);
      const data = await classroomsApi.getClassroomById(classroomId);
      setClassroom(data);
      setFormData({
        name: data.name,
        description: data.description || '',
        course_id: data.course_id,
        grade: data.grade,
        start_date: data.start_date ? data.start_date.split('T')[0] : '',
        end_date: data.end_date ? data.end_date.split('T')[0] : '',
        fee_per_session: parseFloat(data.fee_per_session.toString()),
        max_students: data.max_students,
        is_active: data.is_active,
      });
    } catch (error) {
      console.error('Error fetching classroom:', error);
      toast.error('Không thể tải thông tin lớp học');
      router.push('/teacher/classes');
    } finally {
      setLoading(false);
    }
  };

  const fetchActiveCourses = async () => {
    try {
      setCoursesLoading(true);
      const response = await coursesApi.getCourses(0, 100);
      const data = response.items;
      setCourses(data);
    } catch (error) {
      console.error('Error fetching courses:', error);
      toast.error('Không thể tải danh sách khóa học');
    } finally {
      setCoursesLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    let finalValue: any = value;
    
    if (type === 'number') {
      finalValue = parseFloat(value) || 0;
    } else if (type === 'checkbox') {
      finalValue = (e.target as HTMLInputElement).checked;
    }
    
    setFormData(prev => ({ ...prev, [name]: finalValue }));
    
    // Clear error when user starts typing
    if (errors[name as keyof typeof formData]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Tên lớp học là bắt buộc';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Tên lớp học phải có ít nhất 2 ký tự';
    }

    if (!formData.course_id || formData.course_id === 0) {
      newErrors.course_id = 'Vui lòng chọn khóa học';
    }

    if (formData.start_date && formData.end_date && new Date(formData.end_date) <= new Date(formData.start_date)) {
      newErrors.end_date = 'Ngày kết thúc phải sau ngày bắt đầu';
    }

    if (!formData.fee_per_session || formData.fee_per_session <= 0) {
      newErrors.fee_per_session = 'Học phí phải lớn hơn 0';
    }

    if (!formData.max_students || formData.max_students <= 0) {
      newErrors.max_students = 'Sĩ số tối đa phải lớn hơn 0';
    } else if (formData.max_students > 100) {
      newErrors.max_students = 'Sĩ số tối đa không được vượt quá 100';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setSaving(true);
      await classroomsApi.updateClassroom(classroomId, formData);
      toast.success('Cập nhật lớp học thành công!');
      router.push('/teacher/classes');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Cập nhật thất bại';
      toast.error(message);
    } finally {
      setSaving(false);
    }
  };

  if (!isTeacher) {
    return null;
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Đang tải thông tin lớp học...</span>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/teacher/classes">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Chỉnh sửa lớp học</h1>
            <p className="text-gray-600">Cập nhật thông tin lớp học: {classroom?.name}</p>
          </div>
        </div>

        {/* Form */}
        <Card>
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <div className="flex items-center">
                <GraduationCap className="mr-2 h-5 w-5" />
                <CardTitle>Thông tin lớp học</CardTitle>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <Input
                label="Tên lớp học"
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                error={errors.name}
                placeholder="VD: Lớp A1 - Sáng"
                required
              />

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Khóa học <span className="text-red-500 ml-1">*</span>
                </label>
                {coursesLoading ? (
                  <div className="flex items-center justify-center py-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="ml-2 text-sm text-gray-500">Đang tải khóa học...</span>
                  </div>
                ) : (
                  <select
                    name="course_id"
                    value={formData.course_id}
                    onChange={handleChange}
                    className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value={0}>Chọn khóa học</option>
                    {courses.map((course) => (
                      <option key={course.id} value={course.id}>
                        {course.name}
                      </option>
                    ))}
                  </select>
                )}
                {errors.course_id && (
                  <p className="text-sm text-red-600">{errors.course_id}</p>
                )}
              </div>

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Mô tả lớp học
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                  className="flex w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Mô tả về lớp học này..."
                />
                {errors.description && (
                  <p className="text-sm text-red-600">{errors.description}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Ngày bắt đầu"
                  type="date"
                  name="start_date"
                  value={formData.start_date}
                  onChange={handleChange}
                  error={errors.start_date}
                />

                <Input
                  label="Ngày kết thúc"
                  type="date"
                  name="end_date"
                  value={formData.end_date}
                  onChange={handleChange}
                  error={errors.end_date}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Học phí mỗi buổi (VNĐ)"
                  type="number"
                  name="fee_per_session"
                  value={formData.fee_per_session}
                  onChange={handleChange}
                  error={errors.fee_per_session}
                  placeholder="100000"
                  min="0"
                  step="1000"
                  required
                />

                <Input
                  label="Sĩ số tối đa"
                  type="number"
                  name="max_students"
                  value={formData.max_students}
                  onChange={handleChange}
                  error={errors.max_students}
                  placeholder="20"
                  min="1"
                  max="100"
                  required
                />
              </div>

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Trạng thái lớp học
                </label>
                <select
                  name="is_active"
                  value={formData.is_active.toString()}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.value === 'true' }))}
                  className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="true">Đang hoạt động</option>
                  <option value="false">Không hoạt động</option>
                </select>
                <p className="text-sm text-gray-500">
                  Lớp học không hoạt động sẽ không thể đăng ký học sinh mới
                </p>
              </div>

              {classroom && (
                <div className="bg-gray-50 p-4 rounded-md space-y-2">
                  <h4 className="font-medium text-gray-900">Thông tin bổ sung</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">ID:</span>
                      <span className="ml-2 font-medium">{classroom.id}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Khóa học hiện tại:</span>
                      <span className="ml-2 font-medium">{classroom.course?.name}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Ngày tạo:</span>
                      <span className="ml-2 font-medium">
                        {new Date(classroom.created_at).toLocaleDateString('vi-VN')}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">Lần cập nhật cuối:</span>
                      <span className="ml-2 font-medium">
                        {new Date(classroom.updated_at).toLocaleDateString('vi-VN')}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>

            <CardFooter className="flex justify-end space-x-4">
              <Link href="/teacher/classes">
                <Button variant="outline" disabled={saving}>
                  Hủy
                </Button>
              </Link>
              <Button
                type="submit"
                loading={saving}
                disabled={saving}
              >
                <Save className="mr-2 h-4 w-4" />
                {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
              </Button>
            </CardFooter>
          </form>
        </Card>

        {/* Warning Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm text-yellow-700">Lưu ý quan trọng</CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-gray-600 space-y-2">
            <p>• Thay đổi khóa học sẽ ảnh hưởng đến việc quản lý học sinh</p>
            <p>• Thay đổi học phí chỉ áp dụng cho các buổi học mới</p>
            <p>• Giảm sĩ số tối đa có thể ảnh hưởng đến học sinh đã đăng ký</p>
            <p>• Vô hiệu hóa lớp học sẽ ngăn đăng ký học sinh mới</p>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
