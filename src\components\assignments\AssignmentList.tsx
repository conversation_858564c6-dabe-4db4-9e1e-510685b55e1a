'use client';

import React, { useState, useEffect } from 'react';
import { Assignment, assignmentsApi } from '@/lib/api/assignments';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';
import { Clock, AlertTriangle, CheckCircle, Calendar, FileText } from 'lucide-react';

interface AssignmentListProps {
  classroomId: number;
  userRole: 'teacher' | 'student';
}

function AssignmentList({ classroomId, userRole }: AssignmentListProps) {
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'active' | 'expired'>('active');

  const { user } = useAuth();

  useEffect(() => {
    fetchAssignments();
  }, [classroomId]);

  const fetchAssignments = async () => {
    try {
      setLoading(true);
      const response = await assignmentsApi.getClassroomAssignments(classroomId, 0, 10);
      setAssignments(response.data || []);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch assignments');
      setAssignments([]);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600">{error}</p>
        <button
          onClick={fetchAssignments}
          className="mt-2 text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  // Filter assignments by deadline
  const now = new Date();
  const activeAssignments = assignments.filter(a => new Date(a.deadline) > now);
  const expiredAssignments = assignments.filter(a => new Date(a.deadline) <= now);
  const currentAssignments = activeTab === 'active' ? activeAssignments : expiredAssignments;

  const getStatusBadge = (assignment: Assignment) => {
    const isExpired = new Date(assignment.deadline) <= now;

    if (isExpired) {
      return (
        <div className="flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs">
          <AlertTriangle className="h-3 w-3" />
          Hết hạn
        </div>
      );
    }

    return (
      <div className="flex items-center gap-1 px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">
        <Clock className="h-3 w-3" />
        Còn hạn
      </div>
    );
  };

  const formatTimeRemaining = (deadline: string) => {
    const deadlineDate = new Date(deadline);
    const diffMs = deadlineDate.getTime() - now.getTime();

    if (diffMs <= 0) {
      const overdue = Math.abs(diffMs);
      const days = Math.floor(overdue / (1000 * 60 * 60 * 24));
      const hours = Math.floor((overdue % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

      if (days > 0) return `Quá hạn ${days} ngày`;
      if (hours > 0) return `Quá hạn ${hours} giờ`;
      return 'Vừa quá hạn';
    }

    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (days > 0) return `Còn ${days} ngày`;
    if (hours > 0) return `Còn ${hours} giờ`;
    return 'Sắp hết hạn';
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <Clock className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">Còn hạn</p>
              <p className="text-2xl font-bold text-green-900">{activeAssignments.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-8 w-8 text-red-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">Hết hạn</p>
              <p className="text-2xl font-bold text-red-900">{expiredAssignments.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <FileText className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-blue-800">Tổng cộng</p>
              <p className="text-2xl font-bold text-blue-900">{assignments.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('active')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'active'
                ? 'border-green-500 text-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Clock className="inline-block mr-2 h-4 w-4" />
            Còn hạn ({activeAssignments.length})
          </button>
          <button
            onClick={() => setActiveTab('expired')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'expired'
                ? 'border-red-500 text-red-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <AlertTriangle className="inline-block mr-2 h-4 w-4" />
            Hết hạn ({expiredAssignments.length})
          </button>
        </nav>
      </div>

      {/* Assignments List */}
      {!assignments || assignments.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-gray-400 text-6xl mb-4">📝</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Chưa có bài tập</h3>
          <p className="text-gray-600 mb-4">
            {userRole === 'teacher'
              ? 'Tạo bài tập đầu tiên để bắt đầu.'
              : 'Chưa có bài tập nào được đăng cho lớp này.'
            }
          </p>
        </div>
      ) : currentAssignments.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-gray-400 text-6xl mb-4">
            {activeTab === 'active' ? '⏰' : '⚠️'}
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {activeTab === 'active' ? 'Không có bài tập còn hạn' : 'Không có bài tập hết hạn'}
          </h3>
          <p className="text-gray-600">
            {activeTab === 'active'
              ? 'Tất cả bài tập đều đã hết hạn.'
              : 'Tất cả bài tập đều còn trong thời hạn.'
            }
          </p>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {currentAssignments.map((assignment) => (
            <div
              key={assignment.id}
              className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <h3 className="font-semibold text-gray-900">{assignment.title}</h3>
                  {getStatusBadge(assignment)}
                </div>

                {assignment.description && (
                  <p className="text-gray-600 text-sm">
                    {assignment.description.length > 100
                      ? assignment.description.substring(0, 100) + '...'
                      : assignment.description
                    }
                  </p>
                )}

                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatTimeRemaining(assignment.deadline)}</span>
                  </div>
                  <span className="text-xs px-2 py-1 bg-gray-100 rounded">
                    {assignment.type === 'homework' ? 'Bài tập' : 'Kiểm tra'}
                  </span>
                </div>

                <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                  <div className="text-sm text-gray-500">
                    Hạn: {new Date(assignment.deadline).toLocaleDateString('vi-VN')}
                  </div>
                  <Link
                    href={`/${userRole}/assignments/${assignment.id}`}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    Xem chi tiết
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default AssignmentList;