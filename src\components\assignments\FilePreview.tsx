'use client';

import React, { useState } from 'react';
import { FileInfo } from '@/lib/api/assignments';
import ImageViewer, { useImageViewerKeyboard } from './ImageViewer';
import { Download, Eye, FileText, Image } from 'lucide-react';
import Button from '@/components/ui/Button';

interface FilePreviewProps {
  file: FileInfo;
  showPreview?: boolean;
}

export default function FilePreview({ file, showPreview = true }: FilePreviewProps) {
  const [showImageViewer, setShowImageViewer] = useState(false);
  
  useImageViewerKeyboard(showImageViewer, () => setShowImageViewer(false));

  const fileName = file.originalName || file.originalname;
  const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
  
  const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'].includes(fileExtension);
  const isPDF = fileExtension === 'pdf';
  const isDocument = ['doc', 'docx', 'txt', 'rtf'].includes(fileExtension);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = () => {
    if (isImage) return <Image className="h-5 w-5 text-blue-600" />;
    if (isPDF) return <FileText className="h-5 w-5 text-red-600" />;
    if (isDocument) return <FileText className="h-5 w-5 text-blue-600" />;
    return <FileText className="h-5 w-5 text-gray-600" />;
  };

  // Get the correct file URL (Cloudinary or local)
  const getFileUrl = () => {
    // If file has a direct URL (Cloudinary), use it
    if (file.url && file.url.startsWith('http')) {
      return file.url;
    }
    // Otherwise use local API endpoint
    return `/api/files/${file.filename}`;
  };

  const handlePreview = () => {
    if (isImage) {
      setShowImageViewer(true);
    } else if (isPDF) {
      // Open PDF in new tab
      window.open(getFileUrl(), '_blank');
    } else {
      // Download other files
      handleDownload();
    }
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = getFileUrl();
    link.download = fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <>
      <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
        <div className="flex items-center flex-1">
          {/* File Icon or Thumbnail */}
          <div className="flex-shrink-0 mr-3">
            {isImage && showPreview ? (
              <div className="relative">
                <img
                  src={getFileUrl()}
                  alt={fileName}
                  className="w-12 h-12 object-cover rounded border cursor-pointer hover:opacity-80 transition-opacity"
                  onClick={handlePreview}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.nextElementSibling?.classList.remove('hidden');
                  }}
                />
                <div className="hidden w-12 h-12 bg-gray-200 rounded border flex items-center justify-center">
                  {getFileIcon()}
                </div>
              </div>
            ) : (
              <div className="w-12 h-12 bg-gray-100 rounded border flex items-center justify-center">
                {getFileIcon()}
              </div>
            )}
          </div>

          {/* File Info */}
          <div className="flex-1 min-w-0">
            <p className="font-medium text-gray-900 truncate">{fileName}</p>
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <span>{formatFileSize(file.size)}</span>
              <span>•</span>
              <span className="uppercase">{fileExtension}</span>
              {file.uploadedAt && (
                <>
                  <span>•</span>
                  <span>{new Date(file.uploadedAt).toLocaleDateString()}</span>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2 ml-3">
          {showPreview && (
            <Button
              onClick={handlePreview}
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              <Eye className="h-4 w-4" />
              {isImage ? 'Xem' : isPDF ? 'Mở' : 'Tải'}
            </Button>
          )}
          
          <Button
            onClick={handleDownload}
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
          >
            <Download className="h-4 w-4" />
            Tải về
          </Button>
        </div>
      </div>

      {/* Image Viewer Modal */}
      {isImage && (
        <ImageViewer
          file={file}
          isOpen={showImageViewer}
          onClose={() => setShowImageViewer(false)}
        />
      )}
    </>
  );
}
