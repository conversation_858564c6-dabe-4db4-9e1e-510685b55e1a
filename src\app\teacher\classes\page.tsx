'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';

import { Classroom, PaginatedResponse } from '@/types';
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils';
import { classroomsApi } from '@/lib/api/classrooms';
import Button from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { ArrowLeft, Plus, Search, RefreshCw, Edit, Trash2, GraduationCap, Users } from 'lucide-react';
import toast from 'react-hot-toast';

export default function ClassesPage() {
  const { isTeacher, isAuthenticated } = useAuth();

  const [classrooms, setClassrooms] = useState<PaginatedResponse<Classroom> | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize] = useState(10);

  // Remove redirect logic - handled by ProtectedRoute

  const fetchClassrooms = useCallback(async () => {
    if (!isTeacher || !isAuthenticated) return; // Don't fetch if not teacher or not authenticated

    try {
      console.log('🏫 Fetching classrooms...', {
        currentPage,
        searchTerm,
        loading
      });
      setLoading(true);

      const data = await classroomsApi.getClassrooms(currentPage, pageSize, searchTerm || undefined);

      console.log('✅ Classrooms fetched successfully:', data);
      setClassrooms(data);
    } catch (error) {
      console.error('❌ Error fetching classrooms:', error);
      toast.error('Không thể tải danh sách lớp học');
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm, pageSize, isTeacher, isAuthenticated]); // Dependencies

  // Fetch classrooms when dependencies change
  useEffect(() => {
    fetchClassrooms();
  }, [fetchClassrooms]); // Use fetchClassrooms as dependency

  const deleteClassroom = async (classroomId: number, classroomName: string) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa lớp học "${classroomName}"?\n\nHành động này sẽ vô hiệu hóa lớp học và không thể hoàn tác.`)) {
      return;
    }

    try {
      await classroomsApi.deleteClassroom(classroomId);
      toast.success('Xóa lớp học thành công');
      fetchClassrooms(); // Refresh list
    } catch (error) {
      console.error('Error deleting classroom:', error);
      toast.error('Không thể xóa lớp học');
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(0); // Reset to first page on new search
    fetchClassrooms();
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Show access denied if not authenticated or not teacher
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Please Login</h1>
          <a href="/login" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            Go to Login
          </a>
        </div>
      </div>
    );
  }

  if (!isTeacher) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="mb-4">You need teacher privileges to access this page.</p>
          <a href="/dashboard" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            Go to Dashboard
          </a>
        </div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div className="flex items-center space-x-2">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Quay lại
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Quản lý lớp học</h1>
              <p className="text-gray-600">Tạo và quản lý các lớp học trong khóa học</p>
            </div>
          </div>
          <Link href="/teacher/classes/create">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Thêm lớp học
            </Button>
          </Link>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-4">
            <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-2">
              <div className="relative flex-grow">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Tìm kiếm theo tên lớp học hoặc khóa học..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <Button type="submit" variant="outline">
                <Search className="mr-2 h-4 w-4" />
                Tìm kiếm
              </Button>
              <Button type="button" variant="outline" onClick={() => fetchClassrooms()}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Làm mới
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Classrooms List */}
        <Card>
          <CardHeader>
            <CardTitle>Danh sách lớp học</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-500">Đang tải...</p>
              </div>
            ) : classrooms && classrooms.items && classrooms.items.length > 0 ? (
              <div className="space-y-4">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Tên lớp học
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Khóa học
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Thời gian
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Học phí/buổi
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Sĩ số
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Trạng thái
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Thao tác
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {classrooms.items.map((classroom) => (
                        <tr key={classroom.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{classroom.name}</div>
                            <div className="text-sm text-gray-500">{classroom.description || 'Không có mô tả'}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{classroom.course?.name || 'N/A'}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {formatDate(classroom.start_date)} - {formatDate(classroom.end_date)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{formatCurrency(classroom.fee_per_session)}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center text-sm text-gray-900">
                              <Users className="mr-1 h-4 w-4" />
                              {classroom.enrolled_count || 0}/{classroom.max_students}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(classroom.is_active ? 'active' : 'inactive')}`}>
                              {classroom.is_active ? 'Đang hoạt động' : 'Không hoạt động'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex items-center justify-end space-x-2">
                              <Link href={`/teacher/classes/${classroom.id}`}>
                                <Button variant="outline" size="sm">
                                  <GraduationCap className="h-4 w-4" />
                                </Button>
                              </Link>
                              
                              <Link href={`/teacher/classes/${classroom.id}/edit`}>
                                <Button variant="outline" size="sm">
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </Link>

                              <Button
                                variant="danger"
                                size="sm"
                                onClick={() => deleteClassroom(classroom.id, classroom.name)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {classrooms.totalPages > 1 && (
                  <div className="flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6">
                    <div className="flex flex-1 justify-between sm:hidden">
                      <Button
                        variant="outline"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 0}
                      >
                        Trước
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage >= classrooms.totalPages - 1}
                      >
                        Sau
                      </Button>
                    </div>
                    <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                      <div>
                        <p className="text-sm text-gray-700">
                          Hiển thị <span className="font-medium">{currentPage * pageSize + 1}</span> đến{' '}
                          <span className="font-medium">
                            {Math.min((currentPage + 1) * pageSize, classrooms.total)}
                          </span>{' '}
                          trong tổng số <span className="font-medium">{classrooms.total}</span> lớp học
                        </p>
                      </div>
                      <div>
                        <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                          <Button
                            variant="outline"
                            className="rounded-l-md"
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 0}
                          >
                            Trước
                          </Button>
                          {Array.from({ length: Math.min(classrooms.totalPages, 5) }, (_, i) => {
                            const pageNum = currentPage < 3 ? i : currentPage - 2 + i;
                            if (pageNum >= classrooms.totalPages) return null;
                            return (
                              <Button
                                key={pageNum}
                                variant={pageNum === currentPage ? 'primary' : 'outline'}
                                onClick={() => handlePageChange(pageNum)}
                                className="rounded-none"
                              >
                                {pageNum + 1}
                              </Button>
                            );
                          })}
                          <Button
                            variant="outline"
                            className="rounded-r-md"
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage >= classrooms.totalPages - 1}
                          >
                            Sau
                          </Button>
                        </nav>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <GraduationCap className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Không tìm thấy lớp học</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm ? 'Không có kết quả phù hợp với tìm kiếm của bạn.' : 'Chưa có lớp học nào. Hãy tạo lớp học đầu tiên.'}
                </p>
                <div className="mt-6">
                  <Link href="/teacher/classes/create">
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Thêm lớp học
                    </Button>
                  </Link>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
