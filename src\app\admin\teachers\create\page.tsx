'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { CreateTeacherRequest } from '@/types';
import { validateEmail, validatePassword, generateRandomPassword } from '@/lib/utils';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/Card';
import { apiClient } from '@/lib/api/client';
import toast from 'react-hot-toast';
import { ArrowLeft, RefreshCw } from 'lucide-react';
import Link from 'next/link';

export default function CreateTeacherPage() {
  const router = useRouter();
  const { isAdmin } = useAuth();
  
  const [formData, setFormData] = useState<CreateTeacherRequest>({
    name: '',
    email: '',
    password: '',
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<CreateTeacherRequest>>({});

  // Redirect if not admin
  React.useEffect(() => {
    if (!isAdmin) {
      router.push('/dashboard');
    }
  }, [isAdmin, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name as keyof CreateTeacherRequest]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const generatePassword = () => {
    const newPassword = generateRandomPassword(8);
    setFormData(prev => ({ ...prev, password: newPassword }));
    toast.success('Đã tạo mật khẩu ngẫu nhiên');
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<CreateTeacherRequest> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Họ tên là bắt buộc';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Họ tên phải có ít nhất 2 ký tự';
    }

    if (!formData.email) {
      newErrors.email = 'Email là bắt buộc';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Email không hợp lệ';
    }

    if (!formData.password) {
      newErrors.password = 'Mật khẩu là bắt buộc';
    } else if (!validatePassword(formData.password)) {
      newErrors.password = 'Mật khẩu phải có ít nhất 6 ký tự';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setLoading(true);
      await apiClient.createTeacher(formData);
      toast.success('Tạo tài khoản giáo viên thành công!');
      router.push('/dashboard');
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Tạo tài khoản thất bại';
      toast.error(message);
    } finally {
      setLoading(false);
    }
  };

  if (!isAdmin) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/dashboard">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Tạo tài khoản giáo viên</h1>
            <p className="text-gray-600">Thêm giáo viên mới vào hệ thống</p>
          </div>
        </div>

        {/* Form */}
        <Card>
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <CardTitle>Thông tin giáo viên</CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <Input
                label="Họ và tên"
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                error={errors.name}
                placeholder="Nguyễn Văn A"
                required
              />

              <Input
                label="Email"
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                error={errors.email}
                placeholder="<EMAIL>"
                helperText="Email sẽ được sử dụng để đăng nhập"
                required
              />

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="block text-sm font-medium text-gray-700">
                    Mật khẩu <span className="text-red-500 ml-1">*</span>
                  </label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={generatePassword}
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Tạo ngẫu nhiên
                  </Button>
                </div>
                <input
                  type="text"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Nhập mật khẩu hoặc tạo ngẫu nhiên"
                  required
                />
                {errors.password && (
                  <p className="text-sm text-red-600">{errors.password}</p>
                )}
                <p className="text-sm text-gray-500">
                  Mật khẩu phải có ít nhất 6 ký tự. Giáo viên có thể đổi mật khẩu sau khi đăng nhập.
                </p>
              </div>
            </CardContent>

            <CardFooter className="flex justify-end space-x-4">
              <Link href="/dashboard">
                <Button variant="outline" disabled={loading}>
                  Hủy
                </Button>
              </Link>
              <Button
                type="submit"
                loading={loading}
                disabled={loading}
              >
                {loading ? 'Đang tạo...' : 'Tạo tài khoản'}
              </Button>
            </CardFooter>
          </form>
        </Card>

        {/* Info Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Lưu ý</CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-gray-600 space-y-2">
            <p>• Tài khoản giáo viên sẽ được kích hoạt ngay sau khi tạo</p>
            <p>• Giáo viên có thể đăng nhập bằng email và mật khẩu được cung cấp</p>
            <p>• Giáo viên có thể thay đổi mật khẩu trong phần cài đặt</p>
            <p>• Giáo viên có quyền tạo lớp học, quản lý học sinh và tính học phí</p>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
