'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { 
  Users, 
  UserCheck, 
  UserX, 
  Save, 
  RotateCcw,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle
} from 'lucide-react';

import { attendanceApi, SessionAttendanceResponse, StudentAttendance, AttendanceRecord } from '@/lib/api/attendance';

interface AttendanceManagerProps {
  sessionId: number;
  onClose?: () => void;
}

export default function AttendanceManager({ sessionId, onClose }: AttendanceManagerProps) {
  const [attendanceData, setAttendanceData] = useState<SessionAttendanceResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [localAttendance, setLocalAttendance] = useState<Map<number, AttendanceRecord>>(new Map());

  useEffect(() => {
    loadAttendanceData();
  }, [sessionId]);

  const loadAttendanceData = async () => {
    try {
      setLoading(true);
      const data = await attendanceApi.getSessionAttendance(sessionId);
      setAttendanceData(data);
      
      // Initialize local attendance state
      const localMap = new Map<number, AttendanceRecord>();
      data.students.forEach(student => {
        if (student.is_marked && student.attendance_status) {
          localMap.set(student.student_id, {
            student_id: student.student_id,
            status: student.attendance_status,
            notes: student.attendance_notes || ''
          });
        }
      });
      setLocalAttendance(localMap);
      
    } catch (error: any) {
      console.error('Failed to load attendance data:', error);
      toast.error('Không thể tải dữ liệu điểm danh');
    } finally {
      setLoading(false);
    }
  };

  const handleAttendanceChange = (studentId: number, status: 'present' | 'absent', notes = '') => {
    const newMap = new Map(localAttendance);
    newMap.set(studentId, {
      student_id: studentId,
      status,
      notes
    });
    setLocalAttendance(newMap);
  };

  const handleSaveAttendance = async () => {
    if (!attendanceData) return;

    try {
      setSaving(true);
      
      // Convert Map to array
      const attendanceRecords = Array.from(localAttendance.values());
      
      if (attendanceRecords.length === 0) {
        toast.error('Vui lòng điểm danh ít nhất một học sinh');
        return;
      }

      const result = await attendanceApi.saveSessionAttendance(sessionId, attendanceRecords);
      
      toast.success(result.message);
      
      // Reload data to get updated state
      await loadAttendanceData();
      
    } catch (error: any) {
      console.error('Failed to save attendance:', error);
      toast.error('Không thể lưu điểm danh');
    } finally {
      setSaving(false);
    }
  };

  const handleResetAttendance = async () => {
    if (!confirm('Bạn có chắc chắn muốn xóa tất cả điểm danh cho buổi học này?')) {
      return;
    }

    try {
      setSaving(true);
      await attendanceApi.resetSessionAttendance(sessionId);
      toast.success('Đã xóa điểm danh thành công');
      
      // Clear local state and reload
      setLocalAttendance(new Map());
      await loadAttendanceData();
      
    } catch (error: any) {
      console.error('Failed to reset attendance:', error);
      toast.error('Không thể xóa điểm danh');
    } finally {
      setSaving(false);
    }
  };

  const getStudentAttendanceStatus = (student: StudentAttendance) => {
    const localRecord = localAttendance.get(student.student_id);
    if (localRecord) {
      return localRecord.status;
    }
    return student.attendance_status;
  };

  const isStudentMarked = (student: StudentAttendance) => {
    return localAttendance.has(student.student_id) || student.is_marked;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Đang tải dữ liệu điểm danh...</span>
      </div>
    );
  }

  if (!attendanceData) {
    return (
      <div className="text-center p-8">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <p className="text-gray-600">Không thể tải dữ liệu điểm danh</p>
      </div>
    );
  }

  const { session, students, total_students, present_count, absent_count, can_take_attendance } = attendanceData;

  return (
    <div className="bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Điểm danh buổi học
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {session.title || 'Buổi học'} - {session.date} ({session.start_time} - {session.end_time})
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <div className={`px-3 py-1 rounded-full text-xs font-medium ${
              session.status === 'in_progress' 
                ? 'bg-green-100 text-green-800' 
                : session.status === 'scheduled'
                ? 'bg-blue-100 text-blue-800'
                : 'bg-gray-100 text-gray-800'
            }`}>
              {session.status === 'in_progress' ? 'Đang diễn ra' : 
               session.status === 'scheduled' ? 'Đã lên lịch' : 
               session.status === 'completed' ? 'Hoàn thành' : session.status}
            </div>
            
            {onClose && (
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-4 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">{total_students}</div>
            <div className="text-sm text-gray-600">Tổng học sinh</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <UserCheck className="h-5 w-5 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-green-600">{present_count}</div>
            <div className="text-sm text-gray-600">Có mặt</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <UserX className="h-5 w-5 text-red-600" />
            </div>
            <div className="text-2xl font-bold text-red-600">{absent_count}</div>
            <div className="text-sm text-gray-600">Vắng mặt</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="h-5 w-5 text-orange-600" />
            </div>
            <div className="text-2xl font-bold text-orange-600">
              {total_students - present_count - absent_count}
            </div>
            <div className="text-sm text-gray-600">Chưa điểm danh</div>
          </div>
        </div>
      </div>

      {/* Attendance Status Warning */}
      {!can_take_attendance && (
        <div className="px-6 py-4 bg-yellow-50 border-b border-yellow-200">
          <div className="flex items-center gap-3">
            <AlertCircle className="h-5 w-5 text-yellow-600" />
            <div>
              <p className="text-sm font-medium text-yellow-800">
                Chỉ có thể điểm danh khi buổi học đang diễn ra
              </p>
              <p className="text-xs text-yellow-700">
                Trạng thái hiện tại: {session.status}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Students List */}
      <div className="px-6 py-4">
        <div className="space-y-3">
          {students.map((student) => {
            const currentStatus = getStudentAttendanceStatus(student);
            const isMarked = isStudentMarked(student);
            
            return (
              <div key={student.student_id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-medium">
                      {student.student_name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900">{student.student_name}</h4>
                    <p className="text-sm text-gray-600">{student.student_email}</p>
                    {student.student_grade && (
                      <p className="text-xs text-gray-500">Lớp: {student.student_grade}</p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  {/* Status Display */}
                  {isMarked && (
                    <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                      currentStatus === 'present' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {currentStatus === 'present' ? (
                        <>
                          <CheckCircle className="h-3 w-3" />
                          Có mặt
                        </>
                      ) : (
                        <>
                          <XCircle className="h-3 w-3" />
                          Vắng mặt
                        </>
                      )}
                    </div>
                  )}
                  
                  {/* Attendance Buttons */}
                  {can_take_attendance && (
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleAttendanceChange(student.student_id, 'present')}
                        className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                          currentStatus === 'present'
                            ? 'bg-green-600 text-white'
                            : 'bg-green-100 text-green-700 hover:bg-green-200'
                        }`}
                      >
                        Có mặt
                      </button>
                      
                      <button
                        onClick={() => handleAttendanceChange(student.student_id, 'absent')}
                        className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                          currentStatus === 'absent'
                            ? 'bg-red-600 text-white'
                            : 'bg-red-100 text-red-700 hover:bg-red-200'
                        }`}
                      >
                        Vắng mặt
                      </button>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Actions */}
      {can_take_attendance && (
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <button
              onClick={handleResetAttendance}
              disabled={saving}
              className="flex items-center gap-2 px-4 py-2 text-red-600 hover:text-red-700 disabled:opacity-50"
            >
              <RotateCcw className="h-4 w-4" />
              Xóa điểm danh
            </button>
            
            <button
              onClick={handleSaveAttendance}
              disabled={saving || localAttendance.size === 0}
              className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="h-4 w-4" />
              {saving ? 'Đang lưu...' : 'Lưu điểm danh'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
