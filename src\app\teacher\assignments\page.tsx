'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardH<PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Link from 'next/link';
import { 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  Plus, 
  Calendar,
  Users,
  FileText,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';
import { assignmentsApi, Assignment } from '@/lib/api/assignments';
import { classroomsApi } from '@/lib/api/classrooms';

interface AssignmentWithClassroom extends Assignment {
  classroom_name: string;
  total_students: number;
  submission_count: number;
}

export default function AssignmentsPage() {
  const [assignments, setAssignments] = useState<AssignmentWithClassroom[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'active' | 'expired'>('active');
  const [classrooms, setClassrooms] = useState<any[]>([]);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch classrooms first
      const classroomsData = await classroomsApi.getClassrooms();
      setClassrooms(classroomsData.items);
      
      // Fetch assignments for each classroom
      const allAssignments: AssignmentWithClassroom[] = [];
      
      for (const classroom of classroomsData.items) {
        try {
          const assignmentsData = await assignmentsApi.getAssignmentsByClassroom(classroom.id);
          const assignmentsWithClassroom = assignmentsData.map(assignment => ({
            ...assignment,
            classroom_name: classroom.name,
            total_students: classroom.studentCount || 0,
            submission_count: assignment.submission_count || 0
          }));
          allAssignments.push(...assignmentsWithClassroom);
        } catch (error) {
          console.error(`Error fetching assignments for classroom ${classroom.id}:`, error);
        }
      }
      
      setAssignments(allAssignments);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const now = new Date();
  const activeAssignments = assignments.filter(a => new Date(a.deadline) > now);
  const expiredAssignments = assignments.filter(a => new Date(a.deadline) <= now);

  const currentAssignments = activeTab === 'active' ? activeAssignments : expiredAssignments;

  const getStatusBadge = (assignment: AssignmentWithClassroom) => {
    const isExpired = new Date(assignment.deadline) <= now;
    const submissionRate = assignment.total_students > 0 
      ? (assignment.submission_count / assignment.total_students) * 100 
      : 0;

    if (isExpired) {
      return (
        <div className="flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs">
          <AlertTriangle className="h-3 w-3" />
          Đã hết hạn
        </div>
      );
    }

    if (submissionRate >= 80) {
      return (
        <div className="flex items-center gap-1 px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">
          <CheckCircle className="h-3 w-3" />
          Tốt ({submissionRate.toFixed(0)}%)
        </div>
      );
    }

    return (
      <div className="flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs">
        <Clock className="h-3 w-3" />
        Đang diễn ra ({submissionRate.toFixed(0)}%)
      </div>
    );
  };

  const formatTimeRemaining = (deadline: string) => {
    const deadlineDate = new Date(deadline);
    const diffMs = deadlineDate.getTime() - now.getTime();
    
    if (diffMs <= 0) {
      const overdue = Math.abs(diffMs);
      const days = Math.floor(overdue / (1000 * 60 * 60 * 24));
      const hours = Math.floor((overdue % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      
      if (days > 0) return `Quá hạn ${days} ngày`;
      if (hours > 0) return `Quá hạn ${hours} giờ`;
      return 'Vừa quá hạn';
    }
    
    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (days > 0) return `Còn ${days} ngày`;
    if (hours > 0) return `Còn ${hours} giờ`;
    return 'Sắp hết hạn';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Quản lý bài tập</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Quản lý bài tập</h1>
        <Link href="/teacher/classes">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Tạo bài tập mới
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Đang diễn ra</p>
                <p className="text-2xl font-bold text-gray-900">{activeAssignments.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-red-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Đã hết hạn</p>
                <p className="text-2xl font-bold text-gray-900">{expiredAssignments.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Tổng bài tập</p>
                <p className="text-2xl font-bold text-gray-900">{assignments.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Lớp học</p>
                <p className="text-2xl font-bold text-gray-900">{classrooms.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('active')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'active'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Clock className="inline-block mr-2 h-4 w-4" />
            Đang diễn ra ({activeAssignments.length})
          </button>
          <button
            onClick={() => setActiveTab('expired')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'expired'
                ? 'border-red-500 text-red-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <AlertTriangle className="inline-block mr-2 h-4 w-4" />
            Đã hết hạn ({expiredAssignments.length})
          </button>
        </nav>
      </div>

      {/* Assignments Grid */}
      {currentAssignments.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">
              {activeTab === 'active' ? 'Không có bài tập đang diễn ra' : 'Không có bài tập đã hết hạn'}
            </h3>
            <p className="mt-2 text-gray-600">
              {activeTab === 'active' 
                ? 'Tạo bài tập mới cho lớp học của bạn.'
                : 'Tất cả bài tập đều còn trong thời hạn.'
              }
            </p>
            {activeTab === 'active' && (
              <div className="mt-6">
                <Link href="/teacher/classes">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Tạo bài tập mới
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {currentAssignments.map((assignment) => (
            <Card key={assignment.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg">{assignment.title}</CardTitle>
                  {getStatusBadge(assignment)}
                </div>
                <p className="text-sm text-gray-600">{assignment.classroom_name}</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-sm text-gray-700 line-clamp-2">{assignment.description}</p>
                  
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{formatTimeRemaining(assignment.deadline)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      <span>{assignment.submission_count}/{assignment.total_students}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-3 border-t">
                    <div className="flex items-center gap-2">
                      <Link href={`/teacher/assignments/${assignment.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          Xem
                        </Button>
                      </Link>
                      <Link href={`/teacher/assignments/${assignment.id}/edit`}>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-1" />
                          Sửa
                        </Button>
                      </Link>
                    </div>
                    
                    <div className="text-xs text-gray-500">
                      {assignment.type === 'homework' ? 'Bài tập' : 'Kiểm tra'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
