import { apiClient } from './client';
import { Course, CreateCourseRequest, UpdateCourseRequest, PaginatedResponse } from '@/types';

export const coursesApi = {
  // Get all courses
  getCourses: async (page = 0, limit = 10): Promise<PaginatedResponse<Course>> => {
    const response = await apiClient.get(`/courses?page=${page}&limit=${limit}`);
    return response.data.data; // Backend returns { success: true, data: {...} }
  },

  // Get course by ID
  getCourseById: async (id: number): Promise<Course> => {
    const response = await apiClient.get(`/courses/${id}`);
    return response.data.data;
  },

  // Create new course
  createCourse: async (data: CreateCourseRequest): Promise<Course> => {
    const response = await apiClient.post('/courses', data);
    return response.data.data;
  },

  // Update course
  updateCourse: async (id: number, data: UpdateCourseRequest): Promise<Course> => {
    const response = await apiClient.put(`/courses/${id}`, data);
    return response.data.data;
  },

  // Delete course
  deleteCourse: async (id: number): Promise<{ message: string }> => {
    const response = await apiClient.delete(`/courses/${id}`);
    return response.data;
  },
};
