'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { LoginRequest } from '@/types';
import { validateEmail } from '@/lib/utils';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/Card';

export default function LoginForm() {
  const { login, loading, user, isAuthenticated, isAdmin, isTeacher, isStudent } = useAuth();

  const [formData, setFormData] = useState<LoginRequest>({
    email: '',
    password: '',
  });

  const [errors, setErrors] = useState<Partial<LoginRequest>>({});

  // Show success message and manual navigation after login
  if (isAuthenticated && user) {
    return (
      <div className="text-center">
        <h2 className="text-2xl font-bold text-green-600 mb-4">Login Successful!</h2>
        <p className="mb-4">Welcome back, {user.name}!</p>
        <div className="space-y-2">
          {isAdmin && (
            <a href="/admin" className="block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
              Go to Admin Dashboard
            </a>
          )}
          {isTeacher && (
            <a href="/teacher/classes" className="block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
              Go to Teacher Dashboard
            </a>
          )}
          {isStudent && (
            <a href="/student" className="block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
              Go to Student Dashboard
            </a>
          )}
          <a href="/dashboard" className="block bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
            Go to Dashboard
          </a>
        </div>
      </div>
    );
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name as keyof LoginRequest]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<LoginRequest> = {};

    if (!formData.email) {
      newErrors.email = 'Email là bắt buộc';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Email không hợp lệ';
    }

    if (!formData.password) {
      newErrors.password = 'Mật khẩu là bắt buộc';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      await login(formData);
      // Redirect will be handled by useEffect after login success
    } catch (error) {
      // Error is handled by AuthContext
      console.error('Login error:', error);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Đăng nhập hệ thống
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Quản lý học sinh và lớp học
          </p>
        </div>

        <Card>
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <CardTitle>Thông tin đăng nhập</CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <Input
                label="Email"
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                error={errors.email}
                placeholder="<EMAIL>"
                required
              />

              <Input
                label="Mật khẩu"
                type="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                error={errors.password}
                placeholder="••••••••"
                required
              />
            </CardContent>

            <CardFooter className="flex flex-col space-y-4">
              <Button
                type="submit"
                className="w-full"
                loading={loading}
                disabled={loading}
              >
                {loading ? 'Đang đăng nhập...' : 'Đăng nhập'}
              </Button>

              <div className="text-center text-sm">
                <span className="text-gray-600">Chưa có tài khoản? </span>
                <Link 
                  href="/register" 
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  Đăng ký ngay
                </Link>
              </div>
            </CardFooter>
          </form>
        </Card>

        {/* Demo accounts */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Tài khoản demo</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-xs">
            <div className="grid grid-cols-3 gap-2">
              <div className="text-center p-2 bg-blue-50 rounded">
                <div className="font-medium">Admin</div>
                <div><EMAIL></div>
                <div>123456</div>
              </div>
              <div className="text-center p-2 bg-green-50 rounded">
                <div className="font-medium">Teacher</div>
                <div><EMAIL></div>
                <div>123456</div>
              </div>
              <div className="text-center p-2 bg-yellow-50 rounded">
                <div className="font-medium">Student</div>
                <div><EMAIL></div>
                <div>123456</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
