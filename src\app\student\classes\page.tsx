'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
// import DashboardLayout from '@/components/layout/DashboardLayout';
import { Classroom, PaginatedResponse } from '@/types';
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Search, BookOpen, Users, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import toast from 'react-hot-toast';
import { enrollmentsApi } from '@/lib/api/enrollments';

interface AvailableClassroom extends Classroom {
  enrollment_status: 'pending' | 'approved' | 'rejected' | null;
  enrolled_count: number;
  can_enroll: boolean;
}

export default function StudentClassesPage() {
  const router = useRouter();
  const { user, isStudent } = useAuth();
  
  const [classrooms, setClassrooms] = useState<PaginatedResponse<AvailableClassroom> | null>(null);
  const [loading, setLoading] = useState(true);
  const [enrolling, setEnrolling] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(0);

  // Redirect if not student
  useEffect(() => {
    if (!isStudent) {
      router.push('/dashboard');
    }
  }, [isStudent, router]);

  // Check if user has grade set
  useEffect(() => {
    if (isStudent && user && !user.grade) {
      toast.error('Bạn chưa thiết lập lớp học. Vui lòng liên hệ quản trị viên.');
    }
  }, [isStudent, user]);

  // Fetch available classrooms
  useEffect(() => {
    if (isStudent && user?.grade) {
      fetchClassrooms();
    }
  }, [isStudent, user, currentPage, searchTerm]);

  const fetchClassrooms = async () => {
    try {
      setLoading(true);

      const response = await enrollmentsApi.getAvailableClassrooms(currentPage, 10, searchTerm);

      setClassrooms(response);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching classrooms:', error);
      toast.error('Không thể tải danh sách lớp học');
      setLoading(false);
    }
  };

  const handleEnroll = async (classroomId: number) => {
    try {
      setEnrolling(classroomId);

      await enrollmentsApi.enrollInClassroom(classroomId);

      toast.success('Đăng ký thành công! Vui lòng chờ giáo viên duyệt.');
      fetchClassrooms(); // Refresh list
      setEnrolling(null);
    } catch (error: any) {
      console.error('Enrollment error:', error);
      toast.error(error.message || 'Đăng ký thất bại');
      setEnrolling(null);
    }
  };

  const getEnrollmentStatusIcon = (status: string | null) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  const getEnrollmentStatusText = (status: string | null) => {
    switch (status) {
      case 'pending':
        return 'Chờ duyệt';
      case 'approved':
        return 'Đã duyệt';
      case 'rejected':
        return 'Bị từ chối';
      default:
        return null;
    }
  };

  if (!isStudent) {
    return null;
  }

  if (!user?.grade) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="mx-auto h-12 w-12 text-yellow-500" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">Chưa thiết lập lớp học</h3>
        <p className="mt-2 text-gray-600">
          Bạn cần thiết lập lớp học trước khi có thể xem các lớp học có sẵn.
        </p>
        <p className="mt-1 text-sm text-gray-500">
          Vui lòng liên hệ quản trị viên để cập nhật thông tin lớp học.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Lớp học có sẵn</h1>
            <p className="text-gray-600">
              Tìm và đăng ký các lớp học phù hợp với lớp {user.grade}
            </p>
          </div>
        </div>

        {/* Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Tìm kiếm lớp học..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <Button variant="outline">
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Classrooms List */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 rounded-lg h-64"></div>
              </div>
            ))}
          </div>
        ) : classrooms && classrooms.items.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {classrooms.items.map((classroom) => (
              <Card key={classroom.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg">{classroom.name}</CardTitle>
                      <p className="text-sm text-gray-600 mt-1">
                        {classroom.course?.name}
                      </p>
                    </div>
                    {classroom.enrollment_status && (
                      <div className="flex items-center space-x-1">
                        {getEnrollmentStatusIcon(classroom.enrollment_status)}
                        <span className={`text-xs font-medium ${
                          classroom.enrollment_status === 'approved' ? 'text-green-600' :
                          classroom.enrollment_status === 'pending' ? 'text-yellow-600' :
                          'text-red-600'
                        }`}>
                          {getEnrollmentStatusText(classroom.enrollment_status)}
                        </span>
                      </div>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <p className="text-sm text-gray-600">
                      {classroom.description || 'Không có mô tả'}
                    </p>
                    
                    <div className="flex items-center text-sm text-gray-500">
                      <Users className="h-4 w-4 mr-1" />
                      <span>{classroom.enrolled_count}/{classroom.max_students} học sinh</span>
                    </div>
                    
                    <div className="flex items-center text-sm text-gray-500">
                      <BookOpen className="h-4 w-4 mr-1" />
                      <span>Giáo viên: {classroom.teacher?.name}</span>
                    </div>
                    
                    {classroom.start_date && classroom.end_date && (
                      <div className="text-sm text-gray-500">
                        <span>Thời gian: {formatDate(classroom.start_date)} - {formatDate(classroom.end_date)}</span>
                      </div>
                    )}
                    
                    <div className="text-lg font-semibold text-blue-600">
                      {formatCurrency(parseFloat(classroom.fee_per_session))}/buổi
                    </div>
                    
                    <div className="pt-2">
                      {classroom.can_enroll ? (
                        <Button
                          onClick={() => handleEnroll(classroom.id)}
                          loading={enrolling === classroom.id}
                          disabled={enrolling === classroom.id}
                          className="w-full"
                        >
                          {enrolling === classroom.id ? 'Đang đăng ký...' : 'Đăng ký'}
                        </Button>
                      ) : classroom.enrollment_status ? (
                        <Button variant="outline" disabled className="w-full">
                          Đã đăng ký
                        </Button>
                      ) : (
                        <Button variant="outline" disabled className="w-full">
                          Đã đầy
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                Không tìm thấy lớp học
              </h3>
              <p className="mt-2 text-gray-600">
                {searchTerm 
                  ? `Không có lớp học nào phù hợp với từ khóa "${searchTerm}"`
                  : `Hiện tại chưa có lớp học nào dành cho lớp ${user.grade}`
                }
              </p>
              {searchTerm && (
                <div className="mt-4">
                  <Button variant="outline" onClick={() => setSearchTerm('')}>
                    Xóa bộ lọc
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
  );
}
