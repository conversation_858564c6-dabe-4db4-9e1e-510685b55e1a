'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
// import DashboardLayout from '@/components/layout/DashboardLayout';
import { Course, Classroom, PaginatedResponse } from '@/types';
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils';
import { coursesApi } from '@/lib/api/courses';
import { classroomsApi } from '@/lib/api/classrooms';
import Button from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { ArrowLeft, Edit, Plus, GraduationCap, BookOpen, Calendar, DollarSign, Users } from 'lucide-react';
import toast from 'react-hot-toast';

export default function CourseDetailPage() {
  const router = useRouter();
  const params = useParams();
  const { isTeacher } = useAuth();
  const courseId = parseInt(params.id as string);
  
  const [course, setCourse] = useState<Course | null>(null);
  const [classrooms, setClassrooms] = useState<PaginatedResponse<Classroom> | null>(null);
  const [loading, setLoading] = useState(true);
  const [classroomsLoading, setClassroomsLoading] = useState(true);

  // Redirect if not teacher
  useEffect(() => {
    if (!isTeacher) {
      router.push('/dashboard');
    }
  }, [isTeacher, router]);

  // Fetch course and classrooms data
  useEffect(() => {
    if (isTeacher && courseId) {
      fetchCourse();
      fetchClassrooms();
    }
  }, [isTeacher, courseId]);

  const fetchCourse = async () => {
    try {
      setLoading(true);
      const data = await coursesApi.getCourseById(courseId);
      setCourse(data);
    } catch (error) {
      console.error('Error fetching course:', error);
      toast.error('Không thể tải thông tin khóa học');
      router.push('/teacher/courses');
    } finally {
      setLoading(false);
    }
  };

  const fetchClassrooms = async () => {
    try {
      setClassroomsLoading(true);
      // Get classrooms for this course
      const data = await classroomsApi.getClassrooms(0, 100);
      // Filter by course_id on frontend (ideally should be done on backend)
      const filteredClassrooms = {
        ...data,
        items: data.items.filter((classroom: Classroom) => classroom.course_id === courseId)
      };
      setClassrooms(filteredClassrooms);
    } catch (error) {
      console.error('Error fetching classrooms:', error);
      toast.error('Không thể tải danh sách lớp học');
    } finally {
      setClassroomsLoading(false);
    }
  };

  if (!isTeacher) {
    return null;
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Đang tải thông tin khóa học...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div className="flex items-center space-x-2">
            <Link href="/teacher/courses">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Quay lại
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{course?.name}</h1>
              <p className="text-gray-600">Chi tiết khóa học và danh sách lớp học</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Link href={`/teacher/courses/${courseId}/edit`}>
              <Button variant="outline">
                <Edit className="mr-2 h-4 w-4" />
                Chỉnh sửa
              </Button>
            </Link>
            <Link href="/teacher/classes/create">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Thêm lớp học
              </Button>
            </Link>
          </div>
        </div>

        {/* Course Info */}
        {course && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Course Details */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <div className="flex items-center">
                    <BookOpen className="mr-2 h-5 w-5" />
                    <CardTitle>Thông tin khóa học</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{course.name}</h3>
                    <p className="text-gray-600 mt-1">{course.description || 'Không có mô tả'}</p>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Ngày bắt đầu</p>
                      <p className="text-sm text-gray-900">{formatDate(course.start_date)}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Ngày kết thúc</p>
                      <p className="text-sm text-gray-900">{formatDate(course.end_date)}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Trạng thái</p>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(course.is_active ? 'active' : 'inactive')}`}>
                        {course.is_active ? 'Đang hoạt động' : 'Không hoạt động'}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Ngày tạo</p>
                      <p className="text-sm text-gray-900">{formatDate(course.created_at)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Course Stats */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Thống kê</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center">
                    <GraduationCap className="h-5 w-5 text-blue-600 mr-2" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {classrooms?.items?.length || 0} lớp học
                      </p>
                      <p className="text-xs text-gray-500">Tổng số lớp học</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-green-600 mr-2" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">0 học sinh</p>
                      <p className="text-xs text-gray-500">Đã đăng ký</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <DollarSign className="h-5 w-5 text-purple-600 mr-2" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {classrooms?.items?.length ? 
                          formatCurrency(
                            classrooms.items.reduce((sum, classroom) => 
                              sum + parseFloat(classroom.fee_per_session.toString()), 0
                            ) / classrooms.items.length
                          ) : '0 ₫'
                        }
                      </p>
                      <p className="text-xs text-gray-500">Học phí TB/buổi</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Classrooms List */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <GraduationCap className="mr-2 h-5 w-5" />
                <CardTitle>Danh sách lớp học</CardTitle>
              </div>
              <Link href="/teacher/classes/create">
                <Button size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Thêm lớp học
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            {classroomsLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-500">Đang tải danh sách lớp học...</p>
              </div>
            ) : classrooms && classrooms.items && classrooms.items.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tên lớp học
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Thời gian
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Học phí/buổi
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Sĩ số
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Trạng thái
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Thao tác
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {classrooms.items.map((classroom) => (
                      <tr key={classroom.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{classroom.name}</div>
                          <div className="text-sm text-gray-500">{classroom.description || 'Không có mô tả'}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {classroom.start_date && classroom.end_date ? 
                              `${formatDate(classroom.start_date)} - ${formatDate(classroom.end_date)}` :
                              'Chưa thiết lập'
                            }
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{formatCurrency(classroom.fee_per_session)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm text-gray-900">
                            <Users className="mr-1 h-4 w-4" />
                            {classroom.enrolled_count || 0}/{classroom.max_students}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(classroom.is_active ? 'active' : 'inactive')}`}>
                            {classroom.is_active ? 'Đang hoạt động' : 'Không hoạt động'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center justify-end space-x-2">
                            <Link href={`/teacher/classes/${classroom.id}`}>
                              <Button variant="outline" size="sm">
                                <GraduationCap className="h-4 w-4" />
                              </Button>
                            </Link>
                            
                            <Link href={`/teacher/classes/${classroom.id}/edit`}>
                              <Button variant="outline" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                            </Link>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8">
                <GraduationCap className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Chưa có lớp học</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Hãy tạo lớp học đầu tiên cho khóa học này.
                </p>
                <div className="mt-6">
                  <Link href="/teacher/classes/create">
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Thêm lớp học
                    </Button>
                  </Link>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
  );
}
