'use client';

import React, { useState } from 'react';
import { Assignment, assignmentsApi } from '@/lib/api/assignments';
import { useAuth } from '@/context/AuthContext';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import SimpleFileUpload from './SimpleFileUpload';
import FilePreview from './FilePreview';
import { Upload, FileText, Trash2, Lock, Unlock } from 'lucide-react';
import toast from 'react-hot-toast';

interface SolutionFilesProps {
  assignment: Assignment;
  onUpdate?: () => void;
}

export default function SolutionFiles({ assignment, onUpdate }: SolutionFilesProps) {
  const { user } = useAuth();
  const [uploading, setUploading] = useState(false);
  const [showUpload, setShowUpload] = useState(false);

  const isTeacher = user?.role === 'teacher';
  const isStudent = user?.role === 'student';
  const canViewSolution = assignment.can_view_solution || (isTeacher && (assignment.solution_files?.length || 0) > 0);
  const solutionFiles = assignment.solution_files || [];

  const handleUploadSolution = async (files: File[]) => {
    if (!isTeacher) return;

    try {
      setUploading(true);
      await assignmentsApi.uploadSolutionFiles(assignment.id, files);
      toast.success('Đã upload đáp án thành công!');
      setShowUpload(false);
      onUpdate?.();
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Lỗi khi upload đáp án');
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteSolution = async (fileIndex: number) => {
    if (!isTeacher) return;

    if (!confirm('Bạn có chắc muốn xóa file đáp án này?')) return;

    try {
      await assignmentsApi.deleteSolutionFile(assignment.id, fileIndex);
      toast.success('Đã xóa file đáp án!');
      onUpdate?.();
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Lỗi khi xóa file đáp án');
    }
  };



  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            {canViewSolution ? (
              <Unlock className="mr-2 h-5 w-5 text-green-600" />
            ) : (
              <Lock className="mr-2 h-5 w-5 text-gray-400" />
            )}
            Đáp án bài tập
            {solutionFiles.length > 0 && (
              <span className="ml-2 text-sm text-gray-500">({solutionFiles.length} file)</span>
            )}
          </CardTitle>
          
          {isTeacher && (
            <div className="flex gap-2">
              {!showUpload && (
                <Button
                  onClick={() => setShowUpload(true)}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Upload đáp án
                </Button>
              )}
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Upload Section for Teachers */}
        {isTeacher && showUpload && (
          <div className="mb-6 p-4 border border-blue-200 rounded-lg bg-blue-50">
            <h4 className="font-medium text-blue-900 mb-3">Upload file đáp án</h4>
            <SimpleFileUpload
              onFilesSelected={handleUploadSolution}
              uploading={uploading}
              accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.zip,.rar"
              maxFiles={10}
              maxSize={50 * 1024 * 1024} // 50MB
            />
            <div className="flex gap-2 mt-3">
              <Button
                onClick={() => setShowUpload(false)}
                variant="outline"
                size="sm"
              >
                Hủy
              </Button>
            </div>
          </div>
        )}

        {/* Solution Files Display */}
        {canViewSolution ? (
          solutionFiles.length > 0 ? (
            <div className="space-y-3">
              <p className="text-sm text-gray-600 mb-4">
                {isStudent ? 
                  '✅ Đáp án đã được công bố. Bạn có thể tải về để tham khảo.' :
                  'Danh sách file đáp án đã upload:'
                }
              </p>
              
              {solutionFiles.map((file, index) => (
                <div key={index} className="relative">
                  <FilePreview file={file} showPreview={true} />

                  {isTeacher && (
                    <div className="absolute top-2 right-2">
                      <Button
                        onClick={() => handleDeleteSolution(index)}
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 bg-white shadow-sm"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                {isTeacher ? 'Chưa có đáp án' : 'Chưa có đáp án được công bố'}
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {isTeacher ? 
                  'Upload file đáp án để học sinh có thể tham khảo sau deadline.' :
                  'Đáp án sẽ được công bố sau khi hết hạn nộp bài.'
                }
              </p>
            </div>
          )
        ) : (
          <div className="text-center py-8">
            <Lock className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Đáp án chưa được công bố</h3>
            <p className="mt-1 text-sm text-gray-500">
              Đáp án sẽ được mở khóa sau khi hết hạn nộp bài ({new Date(assignment.deadline).toLocaleString()})
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
